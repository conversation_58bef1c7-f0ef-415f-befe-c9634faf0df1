#!/usr/bin/env node

/**
 * 演示新的混合命名策略效果
 * 展示新旧命名方式的对比
 */

// 导入编译后的工具函数
const { generateReportFilename, generateReportFilePath, extractTimestampFromFilename, extractDateFromFilename } = require('./dist/utils/index.js');

console.log("🎯 HTML统计报告命名逻辑演示\n");

// 模拟不同时间点的报告生成
const testDates = [
  new Date('2025-09-11T08:00:00.000Z'), // 早上8点定时任务
  new Date('2025-09-11T14:32:27.277Z'), // 下午手动执行
  new Date('2025-09-12T08:00:00.123Z'), // 第二天定时任务
];

console.log("📊 新旧命名方式对比：\n");

testDates.forEach((date, index) => {
  console.log(`⏰ 时间点 ${index + 1}: ${date.toLocaleString()}`);
  
  // 旧命名方式（纯时间戳）
  const oldStyleName = `statistics-${date.getTime()}.html`;
  
  // 新命名方式（混合策略）
  const newStyleName = generateReportFilename("statistics", "html", date);
  const newStylePath = generateReportFilePath("statistics", "html", "./database", date);
  
  console.log(`  旧方式: ${oldStyleName}`);
  console.log(`  新方式: ${newStyleName}`);
  console.log(`  完整路径: ${newStylePath}`);
  
  // 展示可读性提升
  const extractedDate = extractDateFromFilename(newStyleName);
  const extractedTimestamp = extractTimestampFromFilename(newStyleName);
  
  console.log(`  📅 可提取日期: ${extractedDate}`);
  console.log(`  🕐 可提取时间: ${new Date(extractedTimestamp).toLocaleString()}`);
  console.log("");
});

console.log("🔗 关联文件命名演示：\n");

const demoDate = new Date();
const htmlFile = generateReportFilename("statistics", "html", demoDate);
const pngFile = generateReportFilename("statistics", "png", demoDate);

console.log(`HTML报告: ${htmlFile}`);
console.log(`PNG图片:  ${pngFile}`);
console.log(`关联性:   ✅ 使用相同的日期和时间戳，便于文件管理\n`);

console.log("📁 文件管理优势：\n");
console.log("✅ 可读性: 文件名包含日期，便于人工识别");
console.log("✅ 排序性: 按日期+时间戳自然排序");
console.log("✅ 唯一性: 毫秒级时间戳确保不重复");
console.log("✅ 关联性: HTML和PNG文件使用相同标识");
console.log("✅ 兼容性: 保持原有的时间戳功能");

console.log("\n🎉 新的混合命名策略已成功实现！");
