/**
 * Created by maoww on 2019/8/20.
 */
define(["jquery", "js/urls", "js/personCenterView/request"], function (
  $,
  urls,
  request
) {
  var commAjax = function (_url, _data, success, error) {
    var _ajaxData = urls.ajaxData({ url: _url, data: _data }, success, error);
    $.ajax(_ajaxData);
  };

  function getRandomNumberByRange(start, end) {
    return Math.floor(Math.random() * (end - start) + start);
  }

  var newSeed = function () {
    var num = getRandomNumberByRange(3, 6);
    var seed = "";
    for (var i = 0; i < num; i++) {
      seed += getRandomNumberByRange(1, 9);
    }
    return parseInt(seed);
  };

  $(document)
    .on("mouseover", ".yzm .sliderDiv", function () {
      $(this).prev().fadeIn(100).addClass("show");
    })
    .on("mouseleave", ".yzm .sliderDiv", function () {
      $(this).prev().fadeOut(100).removeClass("show");
    });

  return {
    init: function (selector) {
      var _this = this;
      var htmlSeed = newSeed();
      request
        .post({
          method: "/netdrv/tmri/captcha/sliderImg",
          params: { seed: htmlSeed },
        })
        .done(function (res) {
          var parent = $(selector);
          $(".orign", parent)
            .attr("src", "data:image/jpeg;base64," + res.data.background)
            .show();
          $(".cut", parent)
            .attr("src", "data:image/jpeg;base64," + res.data.slider)
            .show();

          var arr = res.data.y.split(",");
          var bds = arr[arr.length - 1];

          var callThePageFunction = function (seed) {
            return (seed = seed > 0 ? seed - htmlSeed : seed + htmlSeed);
          };
          eval(bds);
          var y = arr[seed];
          $(".cut", parent).css("top", y + "px");
          var ScrollBar = {
            value: 0,

            maxValue: 100,

            step: 1,

            currentX: 0,

            Initialize: function (parent) {
              $("html").css({
                "touch-action": "none",
                "touch-action": "pan-y",
              });
              var selector = JSON.parse(JSON.stringify(parent));
              parent = $(parent);
              if (this.value > this.maxValue) {
                alert("给定当前值大于了最大值");
                return;
              }

              this.GetValue(parent);
              $(".cut", parent).css("left", 0);
              $(".scroll_tip", parent).show();
              $(".scroll_Track", parent).css("width", this.currentX + "px");
              $(".scroll_Thumb", parent).css(
                "margin-left",
                this.currentX + "px"
              );

              $(".scroll_icon", parent)
                .addClass("icon-arrow-right")
                .removeClass("icon-remove");
              $(".scroll_Thumb", parent).removeAttr("style");
              $(".scroll_Track", parent).css({
                "background-color": "#d2e5f4",
              });

              this.Value(parent, selector);

              $(".scrollBarTxt", parent).html(
                ScrollBar.value + "/" + ScrollBar.maxValue
              );
            },

            Value: function (parent, selector) {
              var valite = false;

              var currentValue;

              var statrX;

              $(".scroll_Thumb", parent).unbind("mousedown");
              $(".scroll_Thumb", parent).mousedown(function (event) {
                valite = true;
                statrX = event.clientX;
                $(".scroll_tip", parent).hide();
                $(document.body).unbind("mousemove");
                $(document.body).mousemove(function (event) {
                  moveFunc(event);
                });
              });
              $(document.body).unbind("mouseup");
              $(document.body).mouseup(function () {
                endFunc();
              });
              var moveFunc = function (event) {
                if (valite == false) return;
                currentValue = event.clientX - statrX + ScrollBar.currentX;
                $(".scroll_Thumb", parent).css(
                  "margin-left",
                  currentValue + "px"
                );

                $(".scroll_Track", parent).css(
                  "width",
                  currentValue + 2 + "px"
                );

                if (currentValue + 26 >= $(".scrollBar", parent).width()) {
                  $(".scroll_Thumb", parent).css(
                    "margin-left",
                    $(".scrollBar", parent).width() - 26 + "px"
                  );

                  $(".scroll_Track", parent).css(
                    "width",
                    $(".scrollBar", parent).width() + 2 + "px"
                  );

                  ScrollBar.value = ScrollBar.maxValue;
                } else if (currentValue <= 0) {
                  $(".scroll_Thumb", parent).css("margin-left", "0px");

                  $(".scroll_Track", parent).css("width", "0px");
                } else {
                  var cv = currentValue || 0;
                  ScrollBar.value = Math.round(
                    100 * (cv / $(".scrollBar", parent).width())
                  );
                }

                $(".scrollBarTxt", parent).html(
                  ScrollBar.value + "/" + ScrollBar.maxValue
                );
                var cut = $(".cut", parent);
                var left = cut.css("left");

                if (
                  ScrollBar.value >=
                  (($(".orign", parent).width() - cut.width()) /
                    $(".orign", parent).width()) *
                    100
                ) {
                  cut.css(
                    "left",
                    $(".orign", parent).width() - cut.width() + "px"
                  );
                } else {
                  cut.css(
                    "left",
                    (ScrollBar.value / ScrollBar.maxValue) * 100 + "%"
                  );
                }
              };
              var endFunc = function () {
                if (valite) {
                  var cut = $(".cut", parent);
                  // _this.init(selector)
                  request
                    .post({
                      method: "/netdrv/tmri/captcha/checkchCoordinate",
                      params: {
                        x: cut.css("left").replace("px", ""),
                        y: cut.css("top").replace("px", ""),
                      },
                    })
                    .done(function (res) {
                      $(".scroll_icon", parent)
                        .removeClass("icon-arrow-right")
                        .addClass("icon-ok");
                      $(".scroll_Thumb", parent).css({
                        color: "#fff",
                        "background-color": "#52ccba",
                      });
                      $(".scroll_Track", parent).css({
                        "background-color": "#d2f4f2",
                      });

                      $(".token", parent).val(res.data);

                      // 取消mouse事件
                      $(".scroll_Thumb", parent).unbind("mousedown");
                      $(document.body).unbind("mousemove");
                      $(document.body).unbind("mouseup");
                      // 取消touch事件
                      $(document).off(
                        "touchstart",
                        selector + " .scroll_Thumb"
                      );
                      $(document).off("touchmove", "body");
                      $(document).off("touchend", "body");

                      $("html").css({
                        "touch-action": "",
                      });
                    })
                    .fail(function (res) {
                      $(".scroll_icon", parent)
                        .removeClass("icon-arrow-right")
                        .addClass("icon-remove");
                      $(".scroll_Thumb", parent).css({
                        color: "#fff",
                        "background-color": "#da7070",
                      });
                      $(".scroll_Track", parent).css({
                        "background-color": "#f4d2d7",
                      });
                      setTimeout(function () {
                        _this.init(selector);
                      }, 800);
                    });
                }

                ScrollBar.value = Math.round(
                  100 * (currentValue / $(".scrollBar", parent).width())
                );
                ScrollBar.currentX =
                  $(".scrollBar", parent).width() *
                    (ScrollBar.value / ScrollBar.maxValue) || 0;
                valite = false;

                if (ScrollBar.value >= ScrollBar.maxValue)
                  ScrollBar.value = ScrollBar.maxValue;

                if (ScrollBar.value <= 0) ScrollBar.value = 0;

                $(".scrollBarTxt", parent).html(
                  ScrollBar.value + "/" + ScrollBar.maxValue
                );
              };

              // 兼容移动端
              $(document).off("touchstart", selector + " .scroll_Thumb");
              $(document).on(
                "touchstart",
                selector + " .scroll_Thumb",
                function (event) {
                  valite = true;
                  statrX = event.originalEvent.touches[0].clientX;
                  $(".scroll_tip", parent).hide();
                  $(document).off("touchmove", "body");
                  $(document).on("touchmove", "body", function (event) {
                    moveFunc(event.originalEvent.touches[0]);
                  });
                }
              );
              $(document).off("touchend", "body");
              $(document).on("touchend", "body", function () {
                endFunc();
              });
            },

            GetValue: function (parent) {
              ScrollBar.currentX =
                $(".scrollBar", parent).width() *
                (ScrollBar.value / ScrollBar.maxValue);
            },
          };
          ScrollBar.Initialize(selector);
        })
        .fail(function (res) {
          alert(res.message);
        });
    },
  };
});
