export interface ReportGenerationOptions {
    enableImageGeneration?: boolean;
    enableUpload?: boolean;
    enablePushNotification?: boolean;
    imageWidth?: number;
}
export interface ReportResult {
    htmlPath?: string;
    imagePath?: string;
    uploadResult?: any;
    pushResult?: boolean;
}
/**
 * 报告生成服务 - 解耦的报告处理逻辑
 */
export declare class ReportService {
    private options;
    constructor(options?: ReportGenerationOptions);
    /**
     * 生成完整报告流程
     */
    generateReport(htmlPath: string, pushKeys?: string[]): Promise<ReportResult>;
    /**
     * HTML转图片
     */
    private convertHtmlToImage;
    /**
     * 上传图片到图床
     */
    private uploadImage;
    /**
     * 发送推送通知
     */
    private sendNotifications;
    /**
     * 更新服务配置
     */
    updateOptions(options: Partial<ReportGenerationOptions>): void;
    /**
     * 获取当前配置
     */
    getOptions(): Required<ReportGenerationOptions>;
}
export default ReportService;
