"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportService = void 0;
const fs_1 = __importDefault(require("fs"));
const child_process_1 = require("child_process");
const index_1 = require("../api/index");
/**
 * 报告生成服务 - 解耦的报告处理逻辑
 */
class ReportService {
    options;
    constructor(options = {}) {
        this.options = {
            enableImageGeneration: options.enableImageGeneration ?? true,
            enableUpload: options.enableUpload ?? true,
            enablePushNotification: options.enablePushNotification ?? true,
            imageWidth: options.imageWidth ?? 730,
        };
    }
    /**
     * 生成完整报告流程
     */
    async generateReport(htmlPath, pushKeys = []) {
        const result = { htmlPath };
        try {
            // 1. 图片转换（可选）
            if (this.options.enableImageGeneration) {
                console.log("开始生成统计图片...");
                result.imagePath = await this.convertHtmlToImage(htmlPath);
            }
            // 2. 上传图床（可选）
            if (this.options.enableUpload && result.imagePath) {
                console.log("开始上传图片到图床...");
                result.uploadResult = await this.uploadImage(result.imagePath);
            }
            // 3. 推送通知（可选）
            if (this.options.enablePushNotification && result.uploadResult && pushKeys.length > 0) {
                console.log("开始推送通知...");
                result.pushResult = await this.sendNotifications(result.uploadResult, pushKeys);
            }
            return result;
        }
        catch (error) {
            console.error("报告生成过程中发生错误:", error);
            throw error;
        }
    }
    /**
     * HTML转图片
     */
    convertHtmlToImage(htmlPath) {
        return new Promise((resolve, reject) => {
            const imagePath = htmlPath.replace('.html', '.png');
            const command = `wkhtmltoimage --width ${this.options.imageWidth} "${htmlPath}" "${imagePath}"`;
            (0, child_process_1.exec)(command, (error, stdout, stderr) => {
                if (error) {
                    console.error(`HTML转图片失败: ${error.message}`);
                    reject(new Error(`HTML转图片失败: ${error.message}`));
                    return;
                }
                if (stderr) {
                    console.warn(`wkhtmltoimage警告: ${stderr}`);
                }
                console.log(`生成统计图片成功: ${imagePath}`);
                resolve(imagePath);
            });
        });
    }
    /**
     * 上传图片到图床
     */
    async uploadImage(imagePath) {
        try {
            const fileBuffer = fs_1.default.readFileSync(imagePath);
            const filename = imagePath.split('/').pop() || 'statistics.png';
            const uploadResult = await (0, index_1.uploadFile)({
                file: fileBuffer,
                filename: filename,
            });
            console.log("图片上传成功:", uploadResult.data?.links?.url);
            return uploadResult;
        }
        catch (error) {
            console.error("图片上传失败:", error);
            throw new Error(`图片上传失败: ${error}`);
        }
    }
    /**
     * 发送推送通知
     */
    async sendNotifications(uploadResult, pushKeys) {
        try {
            const imageUrl = uploadResult.data?.links?.url;
            if (!imageUrl) {
                throw new Error("无法获取图片URL");
            }
            const promises = pushKeys.map(pushKey => (0, index_1.sendMessage)({ pushKey, imageUrl })
                .catch(error => {
                console.error(`推送失败 (${pushKey}):`, error);
                return false;
            }));
            await Promise.allSettled(promises);
            console.log("推送通知完成");
            return true;
        }
        catch (error) {
            console.error("推送通知失败:", error);
            return false;
        }
    }
    /**
     * 更新服务配置
     */
    updateOptions(options) {
        this.options = { ...this.options, ...options };
    }
    /**
     * 获取当前配置
     */
    getOptions() {
        return { ...this.options };
    }
}
exports.ReportService = ReportService;
exports.default = ReportService;
//# sourceMappingURL=reportService.js.map