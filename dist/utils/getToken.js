"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const api_1 = require("../api");
const detect_gaps_1 = require("../detect-gaps");
const htmlSeed = newSeed();
function getRandomNumberByRange(start, end) {
    return Math.floor(Math.random() * (end - start) + start);
}
function newSeed() {
    var num = getRandomNumberByRange(3, 6);
    var seed = "";
    for (var i = 0; i < num; i++) {
        seed += getRandomNumberByRange(1, 9);
    }
    return parseInt(seed);
}
const decryptY = (yCode) => {
    var y_index = 0; // 初始化为默认值
    var seed = htmlSeed;
    var arr = yCode.split(",");
    var bds = arr[arr.length - 1];
    var callThePageFunction = function (seed) {
        return (seed = seed > 0 ? seed - htmlSeed : seed + htmlSeed);
    };
    eval(bds);
    var y = arr[y_index];
    return y;
};
async function getToken({ cookie, province, }) {
    let attempts = 0;
    const MAX_ATTEMPTS = 15;
    while (attempts < MAX_ATTEMPTS) {
        attempts++;
        try {
            await (0, api_1.getCaptchaPre)({ cookie, province });
            const { data } = await (0, api_1.getCaptcha)({
                seed: htmlSeed.toString(),
                cookie,
                province,
            });
            const { background, y: yCode } = JSON.parse(data);
            const y = decryptY(yCode);
            const yValue = parseFloat(y);
            // 识别验证码缺口，根据环境传入测试模式参数
            const gapCoordinates = await (0, detect_gaps_1.detectGaps)(background);
            if (!gapCoordinates || gapCoordinates.length !== 2) {
                throw new Error("未检测到足够的缺口");
            }
            // 使用reduce查找最接近y值的坐标对象
            const closestGap = gapCoordinates.reduce((closest, current) => {
                const currentDiff = Math.abs(yValue - current.topLeft.y);
                const closestDiff = Math.abs(yValue - closest.topLeft.y);
                return currentDiff < closestDiff ? current : closest;
            }, gapCoordinates[0]);
            // 计算缺口的偏移量
            const offsetX = closestGap.topLeft.x - 5;
            const { data: token, code } = await (0, api_1.verifyCaptcha)({
                x: offsetX.toString(),
                y,
                cookie,
                province,
            });
            if (code !== "C100000") {
                throw new Error("验证码验证失败");
            }
            return token;
        }
        catch (error) {
            if (attempts >= MAX_ATTEMPTS) {
                throw new Error(`获取token失败，已尝试${MAX_ATTEMPTS}次: ${error.message}`);
            }
            // 继续下一次循环尝试
        }
    }
}
exports.default = getToken;
//# sourceMappingURL=getToken.js.map