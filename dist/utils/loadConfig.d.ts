interface ConfigType {
    province: string;
    fzjg: string;
    cronSchedule: string;
    databasePath: string;
    daysAhead: number;
    statisticsDays: number;
    pushKey: string[];
    dateRange: {
        startDate: string;
        endDate: string;
    };
    statisticsExaminationName: string;
    features: {
        enableImageGeneration: boolean;
        enableUpload: boolean;
        enablePushNotification: boolean;
        enableStatisticsReport: boolean;
    };
}
declare function loadConfig(): ConfigType;
export default loadConfig;
