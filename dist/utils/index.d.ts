export declare const getProvinceCode: (provinceName: string) => string;
export declare const getLicenseName: (licenseCode: string) => string;
/**
 * 获取本地日期的格式字符串（YYYY-MM-DD）
 * @param date 日期对象，默认为当前日期
 * @returns 本地日期字符串，格式为YYYY-MM-DD
 */
export declare function getLocalDateString(date?: Date): string;
/**
 * 生成统计报告文件名（混合命名策略）
 * @param prefix 文件名前缀，默认为"statistics"
 * @param extension 文件扩展名，默认为"html"
 * @param date 日期对象，默认为当前时间
 * @returns 格式为 {prefix}-{YYYY-MM-DD}-{timestamp}.{extension} 的文件名
 */
export declare function generateReportFilename(prefix?: string, extension?: string, date?: Date): string;
/**
 * 生成统计报告文件路径
 * @param prefix 文件名前缀，默认为"statistics"
 * @param extension 文件扩展名，默认为"html"
 * @param baseDir 基础目录，默认为"./database"
 * @param date 日期对象，默认为当前时间
 * @returns 完整的文件路径
 */
export declare function generateReportFilePath(prefix?: string, extension?: string, baseDir?: string, date?: Date): string;
/**
 * 从混合命名格式的文件名中提取时间戳
 * @param filename 文件名，格式为 prefix-YYYY-MM-DD-timestamp.extension
 * @returns 时间戳数字，如果解析失败返回null
 */
export declare function extractTimestampFromFilename(filename: string): number | null;
/**
 * 从混合命名格式的文件名中提取日期
 * @param filename 文件名，格式为 prefix-YYYY-MM-DD-timestamp.extension
 * @returns 日期字符串（YYYY-MM-DD），如果解析失败返回null
 */
export declare function extractDateFromFilename(filename: string): string | null;
