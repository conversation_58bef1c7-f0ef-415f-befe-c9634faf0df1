{"version": 3, "file": "getToken.js", "sourceRoot": "", "sources": ["../../src/utils/getToken.ts"], "names": [], "mappings": ";;AAAA,gCAAkE;AAClE,gDAA4C;AAE5C,MAAM,QAAQ,GAAG,OAAO,EAAE,CAAC;AAE3B,SAAS,sBAAsB,CAAC,KAAa,EAAE,GAAW;IACxD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAC3D,CAAC;AACD,SAAS,OAAO;IACd,IAAI,GAAG,GAAG,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvC,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,IAAI,IAAI,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxB,CAAC;AAED,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,EAAE;IACjC,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,UAAU;IAC3B,IAAI,IAAI,GAAG,QAAQ,CAAC;IACpB,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE9B,IAAI,mBAAmB,GAAG,UAAU,IAAY;QAC9C,OAAO,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC;IAC/D,CAAC,CAAC;IACF,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;IACrB,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAEF,KAAK,UAAU,QAAQ,CAAC,EACtB,MAAM,EACN,QAAQ,GAIT;IACC,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,MAAM,YAAY,GAAG,EAAE,CAAC;IAExB,OAAO,QAAQ,GAAG,YAAY,EAAE,CAAC;QAC/B,QAAQ,EAAE,CAAC;QAEX,IAAI,CAAC;YACH,MAAM,IAAA,mBAAa,EAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC1C,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAA,gBAAU,EAAC;gBAChC,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE;gBACzB,MAAM;gBACN,QAAQ;aACT,CAAC,CAAC;YACH,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAElD,MAAM,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC1B,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAE7B,uBAAuB;YACvB,MAAM,cAAc,GAAG,MAAM,IAAA,wBAAU,EAAC,UAAU,CAAC,CAAC;YAEpD,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC;YAC/B,CAAC;YAED,uBAAuB;YACvB,MAAM,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;gBAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzD,OAAO,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;YACvD,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtB,WAAW;YACX,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,IAAA,mBAAa,EAAC;gBAChD,CAAC,EAAE,OAAO,CAAC,QAAQ,EAAE;gBACrB,CAAC;gBACD,MAAM;gBACN,QAAQ;aACT,CAAC,CAAC;YAEH,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACvB,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,QAAQ,IAAI,YAAY,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CACb,gBAAgB,YAAY,MAAM,KAAK,CAAC,OAAO,EAAE,CAClD,CAAC;YACJ,CAAC;YACD,YAAY;QACd,CAAC;IACH,CAAC;AACH,CAAC;AAED,kBAAe,QAAQ,CAAC"}