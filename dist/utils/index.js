"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLicenseName = exports.getProvinceCode = void 0;
exports.getLocalDateString = getLocalDateString;
exports.generateReportFilename = generateReportFilename;
exports.generateReportFilePath = generateReportFilePath;
exports.extractTimestampFromFilename = extractTimestampFromFilename;
exports.extractDateFromFilename = extractDateFromFilename;
const province_json_1 = __importDefault(require("../constans/province.json"));
const licenseName_json_1 = __importDefault(require("../constans/licenseName.json"));
const getProvinceCode = (provinceName) => {
    return province_json_1.default[provinceName];
};
exports.getProvinceCode = getProvinceCode;
const getLicenseName = (licenseCode) => {
    return licenseName_json_1.default[licenseCode];
};
exports.getLicenseName = getLicenseName;
/**
 * 获取本地日期的格式字符串（YYYY-MM-DD）
 * @param date 日期对象，默认为当前日期
 * @returns 本地日期字符串，格式为YYYY-MM-DD
 */
function getLocalDateString(date = new Date()) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
}
/**
 * 生成统计报告文件名（混合命名策略）
 * @param prefix 文件名前缀，默认为"statistics"
 * @param extension 文件扩展名，默认为"html"
 * @param date 日期对象，默认为当前时间
 * @returns 格式为 {prefix}-{YYYY-MM-DD}-{timestamp}.{extension} 的文件名
 */
function generateReportFilename(prefix = "statistics", extension = "html", date = new Date()) {
    // 获取日期字符串 YYYY-MM-DD
    const dateStr = getLocalDateString(date);
    // 获取毫秒级时间戳
    const timestamp = date.getTime();
    // 组合文件名：prefix-YYYY-MM-DD-timestamp.extension
    return `${prefix}-${dateStr}-${timestamp}.${extension}`;
}
/**
 * 生成统计报告文件路径
 * @param prefix 文件名前缀，默认为"statistics"
 * @param extension 文件扩展名，默认为"html"
 * @param baseDir 基础目录，默认为"./database"
 * @param date 日期对象，默认为当前时间
 * @returns 完整的文件路径
 */
function generateReportFilePath(prefix = "statistics", extension = "html", baseDir = "./database", date = new Date()) {
    const filename = generateReportFilename(prefix, extension, date);
    return `${baseDir}/${filename}`;
}
/**
 * 从混合命名格式的文件名中提取时间戳
 * @param filename 文件名，格式为 prefix-YYYY-MM-DD-timestamp.extension
 * @returns 时间戳数字，如果解析失败返回null
 */
function extractTimestampFromFilename(filename) {
    // 匹配模式：prefix-YYYY-MM-DD-timestamp.extension
    const match = filename.match(/-(\d{4}-\d{2}-\d{2})-(\d+)\./);
    if (match && match[2]) {
        const timestamp = parseInt(match[2], 10);
        return isNaN(timestamp) ? null : timestamp;
    }
    return null;
}
/**
 * 从混合命名格式的文件名中提取日期
 * @param filename 文件名，格式为 prefix-YYYY-MM-DD-timestamp.extension
 * @returns 日期字符串（YYYY-MM-DD），如果解析失败返回null
 */
function extractDateFromFilename(filename) {
    // 匹配模式：prefix-YYYY-MM-DD-timestamp.extension
    const match = filename.match(/-(\d{4}-\d{2}-\d{2})-\d+\./);
    return match ? match[1] : null;
}
//# sourceMappingURL=index.js.map