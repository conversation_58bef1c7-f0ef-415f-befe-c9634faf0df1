export type ExamCenterListRes = {
    lastPage: number;
    navigatepageNums: number[];
    startRow: number;
    hasNextPage: boolean;
    prePage: number;
    nextPage: number;
    endRow: number;
    pageSize: number;
    list: ExamCenterList[];
    pageNum: number;
    navigatePages: number;
    navigateFirstPage: number;
    total: number;
    pages: number;
    firstPage: number;
    size: number;
    isLastPage: boolean;
    hasPreviousPage: boolean;
    navigateLastPage: number;
    isFirstPage: boolean;
};
export type ExamCenterList = {
    kczt: string;
    fzjg: string;
    pxh: number;
    wddm: string;
    mdzt: string;
    kcdddh: string;
    kkcx: string;
    xh: string;
    kcbj: boolean;
    ywlx: string;
    netSysPlacesite: ExamCenterListNetSysPlacesite;
    kskm: string;
    kkywlx: string;
    ksdd: string;
    zt: string;
    kcmc: string;
};
export type ExamCenterListNetSysPlacesite = {
    glbm: string;
    fzjg: string;
    wddm: string;
    xzqh: string;
    lxdh: string;
    ywfwms: string;
    city: string;
    qybj: string;
    wdlxdm: string;
    ywxzqh: string;
    gps: string;
    gzsjfw: string;
    jlzt: string;
    wdlx: string;
    cjsj: Date;
    lxdz: string;
    ywlx: string;
    fjgjxx: string;
    wdmc: string;
    zbdh: string;
    csbj: string;
    ywfw: string;
    gxsj: Date;
    kcxh: string;
};
export type ExamSessionListRes = {
    glbm: string;
    ycjzrq: Date;
    ksccmc: string;
    lstdkkrs: string;
    lstdyyrs: string;
    yysj: Date;
    ywlx: string;
    kkrs: number;
    ksrq: Date;
    yyrs: number;
    ksdd: string;
    qxrs: number;
    fzjg: string;
    ksddmc: string;
    ksyyrq: Date;
    shbj: string;
    ztmc: string;
    sqrs: number;
    ywlxStr: string;
    kscx: string;
    xh: string;
    kskm: string;
    kscc: number;
    zt: string;
    jhlx: string;
    jzyyrq: Date;
}[];
export type AppointmentResultsRes = {
    xm: string;
    pxsjStr: Date;
    sfzmhm: string;
    zt: string;
    pxsj: number;
    kscx: AppointmentResultsResKscx;
    fkxx?: string;
}[];
export declare enum AppointmentResultsResKscx {
    B2 = "B2",
    C1 = "C1",
    C2 = "C2",
    D = "D"
}
export type ExamCenterOptionsListRes = {
    kczt: string;
    syglbm: string;
    fzjg: string;
    pxh: number;
    kcdddh: string;
    kkcx: string;
    xh: string;
    kcbj: boolean;
    ywlx: string;
    kskm: string;
    kkywlx: string;
    ksdd: string;
    zt: string;
    kcmc: string;
    netSysPlacesite: ExamCenterListNetSysPlacesite;
}[];
export type UploadFileRes = {
    status: boolean;
    message: string;
    data: {
        key: string;
        name: string;
        pathname: string;
        origin_name: string;
        size: number;
        mimetype: string;
        extension: string;
        md5: string;
        sha1: string;
        links: {
            url: string;
            html: string;
            bbcode: string;
            markdown: string;
            markdown_with_link: string;
            thumbnail_url: string;
        };
    };
};
