interface StatisticsData {
    subject1: {
        exam_date: string;
        count: number;
    }[];
    subject4: {
        exam_date: string;
        count: number;
    }[];
    subject2: {
        C1: number;
        C2: number;
        C5: number;
        C1BySession?: {
            [session: string]: number;
        };
        C2BySession?: {
            [session: string]: number;
        };
        C5BySession?: {
            [session: string]: number;
        };
    };
    subject3: {
        C1: number;
        C2: number;
        C5: number;
        C1BySession?: {
            [session: string]: number;
        };
        C2BySession?: {
            [session: string]: number;
        };
        C5BySession?: {
            [session: string]: number;
        };
    };
    subject2ByDate: {
        exam_date: string;
        C1: number;
        C2: number;
        C5: number;
    }[];
    subject3ByDate: {
        exam_date: string;
        C1: number;
        C2: number;
        C5: number;
    }[];
    subject2ByDateAndSession: {
        [exam_date: string]: {
            C1Total: number;
            C2Total: number;
            C5Total: number;
            C1BySessions: {
                [session: string]: number;
            };
            C2BySessions: {
                [session: string]: number;
            };
            C5BySessions: {
                [session: string]: number;
            };
        };
    };
    subject3ByDateAndSession: {
        [exam_date: string]: {
            C1Total: number;
            C2Total: number;
            C5Total: number;
            C1BySessions: {
                [session: string]: number;
            };
            C2BySessions: {
                [session: string]: number;
            };
            C5BySessions: {
                [session: string]: number;
            };
        };
    };
}
/**
 * 数据库操作类
 */
declare class ExamineeDatabase {
    private db;
    private statisticsDays;
    private dbPath;
    constructor(dbPath: string, statisticsDays?: number);
    updateStatisticsDays(days: number): void;
    /**
     * 初始化数据库表
     */
    private initializeDatabase;
    /**
     * 保存数据库到文件
     */
    private saveDatabase;
    /**
     * 插入考生信息
     */
    insertExaminee(data: {
        name: string;
        id_number: string;
        allowed_car_type: string;
        appointment_result: string;
        sort_time: string;
        exam_date: string;
        exam_desc: string;
        exam_car_type: string;
        exam_venue: string;
        detailed_address: string;
        exam_subject: string;
        created_at: string;
    }): void;
    /**
     * 获取统计数据
     */
    getStatistics(examinationName: string): Promise<StatisticsData>;
    /**
     * 获取指定科目的车型统计数据
     */
    private getSubjectCarTypeStats;
    /**
     * 获取指定科目按日期和车型的统计数据
     */
    private getSubjectCarTypeByDateStats;
    /**
     * 获取指定科目按场次和车型的统计数据
     */
    private getSubjectCarTypeBySessionStats;
    /**
     * 获取指定科目按日期和场次的统计数据
     */
    private getSubjectByDateAndSessionStats;
    /**
     * 获取数据库中最远的考试日期
     * @returns 最远的考试日期，如果没有记录则返回 null
     */
    getLatestExamDate(): string | null;
    /**
     * 关闭数据库连接
     */
    close(): void;
    /**
     * 分析数据库并打印统计结果
     */
    printStatistics(examinationName: string): Promise<void>;
    /**
     * 生成Markdown表格格式的统计数据
     */
    generateHTMLTable(examinationName: string): Promise<string>;
    /**
     * 根据日期获取星期几
     */
    private getDayOfWeek;
    /**
     * 将生成的Markdown表格保存到文件
     * @param filePath 文件保存路径
     */
    saveMarkdownTable(filePath: string, licenseName: string): Promise<void>;
    saveHtml(filePath: string, licenseName: string): Promise<void>;
    saveMarkdown2Image(filePath: string): Promise<void>;
}
export default ExamineeDatabase;
