{"version": 3, "file": "detect-gaps.js", "sourceRoot": "", "sources": ["../src/detect-gaps.ts"], "names": [], "mappings": ";;;;;AA6VS,gCAAU;AA7VnB,gDAAwB;AACxB,gDAAwB;AACxB,4CAAoB;AA+BpB,OAAO;AACP,MAAM,iBAAiB,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;AACvE,MAAM,UAAU,GAAG,wBAAwB,CAAC;AAC5C,MAAM,gBAAgB,GAAG,wBAAwB,CAAC;AAElD,oBAAoB;AACpB,MAAM,SAAS,GAAG,CAAC,MAAc,EAAE,MAAc,EAAE,SAAS,GAAG,EAAE,EAAW,EAAE;IAC5E,MAAM,EAAE,GAAG,cAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM,EAAE,GAAG,cAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM,EAAE,GAAG,cAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAEpC,MAAM,EAAE,GAAG,cAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM,EAAE,GAAG,cAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM,EAAE,GAAG,cAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAEpC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CACpB,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CACnE,CAAC;IAEF,OAAO,IAAI,GAAG,SAAS,CAAC;AAC1B,CAAC,CAAC;AAEF,oBAAoB;AACpB,MAAM,oBAAoB,GAAG,CAAC,IAAU,EAAE,IAAU,EAAU,EAAE;IAC9D,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,IAAI,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClE,SAAS,EAAE,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,oBAAoB;AACpB,MAAM,QAAQ,GAAG,CAAC,QAAc,EAAE,UAAgB,EAAY,EAAE;IAC9D,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;IAClC,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IAEpC,QAAQ;IACR,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9E,OAAO;IACP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnD,MAAM,eAAe,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEvD,IAAI,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,EAAE,CAAC;gBAC9C,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED,iBAAiB;IACjB,MAAM,IAAI,GAAa,EAAE,CAAC;IAC1B,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC;SAC9B,IAAI,CAAC,CAAC,CAAC;SACP,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1C,wBAAwB;gBACxB,MAAM,MAAM,GAAW;oBACrB,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,CAAC;oBACT,IAAI,EAAE,CAAC;oBACP,IAAI,EAAE,CAAC;oBACP,UAAU,EAAE,CAAC;iBACd,CAAC;gBAEF,MAAM,KAAK,GAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAClC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAErB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxB,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;oBAC9B,uBAAuB;oBACvB,IAAI,CAAC,OAAO;wBAAE,SAAS;oBAEvB,MAAM,CAAC,UAAU,EAAE,CAAC;oBAEpB,SAAS;oBACT,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;oBACnD,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;oBACnD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC/C,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;oBAE/C,cAAc;oBACd,MAAM,UAAU,GAAG;wBACjB,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI;wBACvB,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI;wBACtB,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,IAAI;wBACvB,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI;qBACvB,CAAC;oBAEF,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;wBAC7B,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;wBAC9B,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;wBAE9B,IACE,EAAE,IAAI,CAAC;4BACP,EAAE,GAAG,KAAK;4BACV,EAAE,IAAI,CAAC;4BACP,EAAE,GAAG,MAAM;4BACX,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC;4BACrB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAChB,CAAC;4BACD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;4BAC7B,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;wBACzB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,kBAAkB;gBAClB,IACE,MAAM,CAAC,UAAU,GAAG,EAAE;oBACtB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC;oBAC/B,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAC/B,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,mBAAmB;IACnB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;IACjD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,YAAY;AACvC,CAAC,CAAC;AAEF,KAAK,UAAU,UAAU,CACvB,mBAAkC,IAAI;IAEtC,IAAI,CAAC;QACH,IAAI,UAAgB,CAAC;QAErB,IAAI,gBAAgB,EAAE,CAAC;YACrB,aAAa;YACb,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;YAC5D,UAAU,GAAG,MAAM,cAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,oBAAoB;YACpB,OAAO,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;YACtC,OAAO;QACT,CAAC;QAED,WAAW;QACX,MAAM,cAAc,GAAoB,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;QAEhD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnD,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;gBACrD,MAAM,KAAK,GAAG,MAAM,cAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACzC,cAAc,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,IAAI;oBACV,KAAK;iBACN,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,aAAa;QACb,IAAI,SAAS,GAAyB,IAAI,CAAC;QAC3C,IAAI,eAAe,GAAG,QAAQ,CAAC;QAE/B,KAAK,MAAM,QAAQ,IAAI,cAAc,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,oBAAoB,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACnE,IAAI,SAAS,GAAG,eAAe,EAAE,CAAC;gBAChC,eAAe,GAAG,SAAS,CAAC;gBAC5B,SAAS,GAAG,QAAQ,CAAC;YACvB,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,SAAS;QACT,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAEnD,SAAS;QACT,MAAM,cAAc,GAAoB,EAAE,CAAC;QAE3C,SAAS;QACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAEpB,SAAS;YACT,cAAc,CAAC,IAAI,CAAC;gBAClB,EAAE,EAAE,CAAC,GAAG,CAAC;gBACT,OAAO,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE;gBACzC,WAAW,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,EAAE;gBACzC,KAAK,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM;gBAC5B,MAAM,EAAE,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM;gBAC7B,UAAU,EAAE,GAAG,CAAC,UAAU;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;QAC1D,IAAI,UAAU,EAAE,CAAC;YACf,gBAAgB;YAChB,MAAM,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAE5C,eAAe;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAEpB,cAAc;gBACd,MAAM,SAAS,GAAG,CAAC,CAAC;gBAEpB,cAAc;gBACd,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;oBACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;wBACnC,MAAM;wBACN,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;4BACxB,WAAW,CAAC,aAAa,CACvB,cAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAC9B,CAAC,EACD,GAAG,CAAC,MAAM,GAAG,CAAC,CACf,CAAC;wBACJ,CAAC;wBACD,MAAM;wBACN,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC;4BAC3C,WAAW,CAAC,aAAa,CACvB,cAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAC9B,CAAC,EACD,GAAG,CAAC,IAAI,GAAG,CAAC,CACb,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,cAAc;gBACd,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;oBACpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;wBACnC,MAAM;wBACN,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;4BACxB,WAAW,CAAC,aAAa,CACvB,cAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAC9B,GAAG,CAAC,MAAM,GAAG,CAAC,EACd,CAAC,CACF,CAAC;wBACJ,CAAC;wBACD,MAAM;wBACN,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC;4BAC1C,WAAW,CAAC,aAAa,CACvB,cAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAC9B,GAAG,CAAC,IAAI,GAAG,CAAC,EACZ,CAAC,CACF,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,sBAAsB;YACtB,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC9C,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1D,MAAM,eAAe,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAEvD,IAAI,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,EAAE,CAAC;wBAC9C,SAAS,CAAC,aAAa,CAAC,cAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC;YACH,CAAC;YAED,WAAW;YACX,MAAM,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,SAAS,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;YAEhD,gBAAgB;YAChB,YAAE,CAAC,aAAa,CACd,gBAAgB,EAChB,IAAI,CAAC,SAAS,CACZ;gBACE,aAAa,EAAE,SAAS,CAAC,IAAI;gBAC7B,IAAI,EAAE,cAAc;aACrB,EACD,IAAI,EACJ,CAAC,CACF,CACF,CAAC;YAEF,oBAAoB;YACpB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAC7B,OAAO,CAAC,GAAG,CACT,GAAG,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC;qBAC9C,QAAQ,EAAE;qBACV,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,KAAK;qBAChE,QAAQ,EAAE;qBACV,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE,CAC/B,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;AACH,CAAC"}