import { defineConfig } from "tsdown";

export default defineConfig({
  entry: ["./src/index.ts"],
  format: "cjs",
  sourcemap: true,
  skipNodeModulesBundle: false,
  noExternal: [/.*/],
  external: [],
  treeshake: true,
  // minify: true, // 启用后 debug 的时候看不到变量名
  platform: "node",
  target: "node22",
  copy: [
    "src/assets/background",
    // dist
    { from: "src/assets/background", to: "dist/assets/background" },
    {
      from: "node_modules/sql.js/dist/sql-wasm.wasm",
      to: "dist/sql-wasm.wasm",
    },
    { from: "app-config.json", to: "dist/app-config.json" },
    // build
    {
      from: "node_modules/sql.js/dist/sql-wasm.wasm",
      to: "build/sql-wasm.wasm",
    },
    { from: "app-config.json", to: "build/app-config.json" },
    { from: "README.md", to: "build/README.md" },
  ],
  fixedExtension: true,
  shims: true,
});
