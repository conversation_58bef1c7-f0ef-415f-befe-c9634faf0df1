#!/usr/bin/env node

/**
 * 测试新的混合命名策略
 * 验证文件名生成逻辑是否正确
 */

// 模拟新的命名函数
function getLocalDateString(date = new Date()) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

function generateReportFilename(
  prefix = "statistics",
  extension = "html",
  date = new Date()
) {
  // 获取日期字符串 YYYY-MM-DD
  const dateStr = getLocalDateString(date);
  
  // 获取毫秒级时间戳
  const timestamp = date.getTime();
  
  // 组合文件名：prefix-YYYY-MM-DD-timestamp.extension
  return `${prefix}-${dateStr}-${timestamp}.${extension}`;
}

function generateReportFilePath(
  prefix = "statistics",
  extension = "html",
  baseDir = "./database",
  date = new Date()
) {
  const filename = generateReportFilename(prefix, extension, date);
  return `${baseDir}/${filename}`;
}

function extractTimestampFromFilename(filename) {
  // 匹配模式：prefix-YYYY-MM-DD-timestamp.extension
  const match = filename.match(/-(\d{4}-\d{2}-\d{2})-(\d+)\./);
  if (match && match[2]) {
    const timestamp = parseInt(match[2], 10);
    return isNaN(timestamp) ? null : timestamp;
  }
  return null;
}

function extractDateFromFilename(filename) {
  // 匹配模式：prefix-YYYY-MM-DD-timestamp.extension
  const match = filename.match(/-(\d{4}-\d{2}-\d{2})-\d+\./);
  return match ? match[1] : null;
}

// 测试函数
function runTests() {
  console.log("🧪 测试新的混合命名策略\n");
  
  // 测试1: 基本文件名生成
  console.log("📝 测试1: 基本文件名生成");
  const testDate = new Date('2025-09-11T14:32:27.277Z');
  const htmlFilename = generateReportFilename("statistics", "html", testDate);
  const pngFilename = generateReportFilename("statistics", "png", testDate);
  
  console.log(`HTML文件名: ${htmlFilename}`);
  console.log(`PNG文件名:  ${pngFilename}`);
  console.log(`预期格式:   statistics-2025-09-11-1726063947277.html\n`);
  
  // 测试2: 文件路径生成
  console.log("📁 测试2: 文件路径生成");
  const htmlPath = generateReportFilePath("statistics", "html", "./database", testDate);
  const pngPath = generateReportFilePath("statistics", "png", "./database", testDate);
  
  console.log(`HTML路径: ${htmlPath}`);
  console.log(`PNG路径:  ${pngPath}\n`);
  
  // 测试3: 时间戳提取
  console.log("🔍 测试3: 时间戳提取");
  const extractedTimestamp = extractTimestampFromFilename(htmlFilename);
  const originalTimestamp = testDate.getTime();
  
  console.log(`原始时间戳: ${originalTimestamp}`);
  console.log(`提取时间戳: ${extractedTimestamp}`);
  console.log(`提取正确:   ${extractedTimestamp === originalTimestamp ? '✅' : '❌'}\n`);
  
  // 测试4: 日期提取
  console.log("📅 测试4: 日期提取");
  const extractedDate = extractDateFromFilename(htmlFilename);
  const originalDate = getLocalDateString(testDate);
  
  console.log(`原始日期: ${originalDate}`);
  console.log(`提取日期: ${extractedDate}`);
  console.log(`提取正确: ${extractedDate === originalDate ? '✅' : '❌'}\n`);
  
  // 测试5: 文件名可读性对比
  console.log("👀 测试5: 文件名可读性对比");
  const oldStyleName = `statistics-${testDate.getTime()}.html`;
  const newStyleName = htmlFilename;
  
  console.log(`旧命名方式: ${oldStyleName}`);
  console.log(`新命名方式: ${newStyleName}`);
  console.log(`可读性提升: ✅ 可以直观看出生成日期\n`);
  
  // 测试6: 唯一性验证
  console.log("🔒 测试6: 唯一性验证");
  const date1 = new Date('2025-09-11T14:32:27.277Z');
  const date2 = new Date('2025-09-11T14:32:27.278Z'); // 相差1毫秒
  
  const filename1 = generateReportFilename("statistics", "html", date1);
  const filename2 = generateReportFilename("statistics", "html", date2);
  
  console.log(`文件名1: ${filename1}`);
  console.log(`文件名2: ${filename2}`);
  console.log(`唯一性: ${filename1 !== filename2 ? '✅' : '❌'}\n`);
  
  // 测试7: 关联文件命名
  console.log("🔗 测试7: 关联文件命名");
  const baseDate = new Date();
  const htmlFile = generateReportFilename("statistics", "html", baseDate);
  const pngFile = generateReportFilename("statistics", "png", baseDate);
  
  // 模拟ReportService的转换逻辑
  const convertedPngFile = htmlFile.replace('.html', '.png');
  
  console.log(`HTML文件: ${htmlFile}`);
  console.log(`PNG文件:  ${pngFile}`);
  console.log(`转换结果: ${convertedPngFile}`);
  console.log(`转换正确: ${pngFile === convertedPngFile ? '✅' : '❌'}\n`);
  
  console.log("🎉 所有测试完成！新的混合命名策略工作正常。");
}

// 运行测试
runTests();
