#!/usr/bin/env node

/**
 * 模拟数据测试新的混合命名策略
 * 在网络不可用时验证命名逻辑
 */

const { generateReportFilePath, extractTimestampFromFilename, extractDateFromFilename } = require('./dist/utils/index.js');
const fs = require('fs');
const path = require('path');

async function createMockReport() {
  console.log("🧪 模拟数据测试 - 新的混合命名策略\n");

  // 确保database目录存在
  const dbDir = './database';
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }

  // 生成新格式的文件名
  const reportDate = new Date();
  const htmlPath = generateReportFilePath("statistics", "html", "./database", reportDate);
  const pngPath = generateReportFilePath("statistics", "png", "./database", reportDate);

  console.log(`📄 生成的HTML路径: ${htmlPath}`);
  console.log(`🖼️  生成的PNG路径:  ${pngPath}`);

  // 创建模拟的HTML内容
  const mockHtmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<style>
  body {
    font-family: "Microsoft YaHei", "SimSun", "Arial", sans-serif;
  }
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 20px 0;
  }
  th, td {
    border: 1px solid #ccc;
    padding: 8px;
    text-align: center;
  }
  th {
    background-color: #f0f0f0;
  }
</style>
</head>
<body>
  <h2 style="text-align:center;">驾考预约统计报告（模拟数据）</h2>
  <p style="text-align:center;">生成时间: ${reportDate.toLocaleString()}</p>
  <p style="text-align:center;">文件名: ${path.basename(htmlPath)}</p>
  
  <table>
    <tr>
      <th>考试日期</th>
      <th>星期</th>
      <th>科目一</th>
      <th>科目二</th>
      <th>科目三</th>
      <th>科目四</th>
    </tr>
    <tr>
      <td>2025-09-11</td>
      <td>星期三</td>
      <td>68人</td>
      <td>97人</td>
      <td>167人</td>
      <td>3人</td>
    </tr>
    <tr>
      <td>2025-09-12</td>
      <td>星期四</td>
      <td>95人</td>
      <td>81人</td>
      <td>133人</td>
      <td>12人</td>
    </tr>
    <tr>
      <td>2025-09-13</td>
      <td>星期五</td>
      <td>61人</td>
      <td>105人</td>
      <td>176人</td>
      <td>13人</td>
    </tr>
  </table>
  
  <div style="margin-top: 30px; padding: 10px; background-color: #f9f9f9; border-radius: 5px;">
    <h3>📊 新命名策略验证</h3>
    <p><strong>文件名格式:</strong> statistics-YYYY-MM-DD-timestamp.html</p>
    <p><strong>优势:</strong></p>
    <ul>
      <li>✅ 可读性: 直观显示生成日期</li>
      <li>✅ 唯一性: 毫秒级时间戳确保不重复</li>
      <li>✅ 排序性: 按日期自然排序</li>
      <li>✅ 关联性: HTML和PNG文件使用相同标识</li>
    </ul>
  </div>
</body>
</html>`;

  try {
    // 写入HTML文件
    fs.writeFileSync(htmlPath, mockHtmlContent, 'utf8');
    console.log(`✅ 模拟HTML报告已生成: ${htmlPath}`);

    // 验证文件名解析
    const extractedDate = extractDateFromFilename(path.basename(htmlPath));
    const extractedTimestamp = extractTimestampFromFilename(path.basename(htmlPath));

    console.log(`\n🔍 文件名解析验证:`);
    console.log(`  📅 提取的日期: ${extractedDate}`);
    console.log(`  🕐 提取的时间戳: ${extractedTimestamp}`);
    console.log(`  ⏰ 转换为时间: ${new Date(extractedTimestamp).toLocaleString()}`);

    // 验证文件大小
    const stats = fs.statSync(htmlPath);
    console.log(`\n📊 文件信息:`);
    console.log(`  📁 文件大小: ${Math.round(stats.size / 1024 * 100) / 100}KB`);
    console.log(`  📅 创建时间: ${stats.birthtime.toLocaleString()}`);

    // 模拟PNG文件名转换（ReportService的逻辑）
    const convertedPngPath = htmlPath.replace('.html', '.png');
    console.log(`\n🔗 关联文件验证:`);
    console.log(`  HTML文件: ${path.basename(htmlPath)}`);
    console.log(`  PNG文件:  ${path.basename(convertedPngPath)}`);
    console.log(`  转换正确: ${convertedPngPath === pngPath ? '✅' : '❌'}`);

    console.log(`\n🎉 模拟测试完成！新的混合命名策略工作正常。`);
    console.log(`\n💡 说明: 虽然网络连接超时导致无法获取真实数据，但新的命名逻辑已经成功实现并正常工作。`);

  } catch (error) {
    console.error(`❌ 模拟测试失败:`, error.message);
  }
}

// 运行模拟测试
createMockReport().catch(console.error);
