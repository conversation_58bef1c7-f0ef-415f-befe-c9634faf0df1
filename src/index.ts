// 检测是否在SEA环境中运行
try {
  // @ts-ignore
  const sea = require("node:sea");
  if (sea && sea.isSea && sea.isSea()) {
    console.log("Running in SEA environment, configuring require");
    // @ts-ignore
    const { createRequire } = require("node:module");
    // @ts-ignore
    global.require = createRequire(import.meta.url);
  }
} catch (error) {
  // 不在SEA环境中，无需特殊处理
}

// 适配SEA环境下的node-cron导入
// @ts-ignore 忽略类型检查
let cron: any;
try {
  // 尝试直接导入
  cron = require("node-cron");
} catch (error) {
  try {
    // 在ESM环境中使用import.meta.url
    const { createRequire } = require("node:module");
    const customRequire = createRequire(import.meta.url);
    cron = customRequire("node-cron");
  } catch (innerError) {
    console.error("无法加载node-cron模块:", innerError);
    throw new Error("无法加载node-cron模块，请确保已正确安装依赖");
  }
}

import fs from "fs";
import {
  getCookie,
  getExamSessionList,
  getExamCenterOptionsList,
  getAppointmentResults,
  uploadFile,
  sendMessage,
} from "./api/index";
import ExamineeDatabase from "./database";
import { getProvinceCode, getLocalDateString } from "./utils";
import loadConfig from "./utils/loadConfig";

// 类型定义
interface ExamineeData {
  xm?: string;
  sfzmhm?: string;
  kscx?: string;
  zt?: string;
  pxsjStr?: string | Date;
  pxsj?: number;
  fkxx?: string;
  [key: string]: any;
}

interface ExamSession {
  xh: string;
  ksrq: string | Date;
  ksccmc: string;
  ksddmc: string;
  kscx: string;
  yyrs: number;
  [key: string]: any;
}

interface ExamCenter {
  ksdd: string;
  kskm: string;
  kcmc: string;
  netSysPlacesite: {
    lxdz: string;
    [key: string]: any;
  };
  [key: string]: any;
}

const CONFIG = loadConfig();

/**
 * 计算查询的日期范围
 * @param db 数据库实例
 * @returns 开始和结束日期
 */
function calculateDateRange(db: ExamineeDatabase): {
  startDate: string;
  endDate: string;
} {
  const today = getLocalDateString();
  let startDate = today;
  let endDate = "";

  // 尝试从数据库获取最新日期
  const latestExamDate = db.getLatestExamDate();

  if (latestExamDate) {
    // 如果数据库有数据，使用最远日期的后一天作为开始日期
    const nextDate = new Date(latestExamDate);
    nextDate.setDate(nextDate.getDate() + 1); // 获取后一天
    startDate = getLocalDateString(nextDate);

    // 计算结束日期为(最远日期+1天) + 配置的天数
    const endDueDate = new Date(nextDate); // 使用已经+1天的日期
    endDueDate.setDate(endDueDate.getDate() + CONFIG.daysAhead - 1); // 因为nextDate已经+1了，这里补偿-1
    endDate = getLocalDateString(endDueDate);
  } else {
    // 如果数据库没有数据，使用当前日期作为开始日期，并计算结束日期
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + CONFIG.daysAhead);
    endDate = getLocalDateString(futureDate);
  }

  return {
    startDate: startDate,
    endDate: endDate,
  };
}

/**
 * 处理考生数据并保存到数据库
 */
async function processExamCenterData(
  examCenter: ExamCenter,
  kskm: string,
  cookie: string,
  provinceCode: string,
  fzjg: string,
  db: ExamineeDatabase
): Promise<void> {
  try {
    const examSessionList = await getExamSessionList({
      cookie,
      province: provinceCode,
      fzjg,
      ksdd: examCenter.ksdd,
      kskm,
      startDate: CONFIG.dateRange.startDate,
      endDate: CONFIG.dateRange.endDate,
    });

    // 获取每个场次的考生详细信息
    console.log(
      `科目${kskm}，正在获取考场 ${examCenter.netSysPlacesite.wdmc} 的考生详细信息...`
    );

    for (const session of examSessionList) {
      await processExamSession(
        session,
        examCenter,
        cookie,
        provinceCode,
        kskm,
        db
      );
    }
  } catch (error) {
    console.error(
      `获取考场${examCenter.netSysPlacesite.wdmc}的考试计划失败:`,
      error
    );
  }
}

/**
 * 处理单个考试场次数据
 */
async function processExamSession(
  session: ExamSession,
  examCenter: ExamCenter,
  cookie: string,
  provinceCode: string,
  kskm: string,
  db: ExamineeDatabase
): Promise<void> {
  try {
    // 获取考生信息
    const appointmentResults = await getAppointmentResults({
      cookie,
      xh: session.xh,
      province: provinceCode,
    });

    console.log(
      `场次 ${session.ksccmc} - 考试日期 ${new Date(
        session.ksrq
      ).toLocaleDateString()} - 获取到考生信息`
    );

    // 将信息写入数据库
    const examDate = new Date(session.ksrq).toISOString().split("T")[0];
    const examDesc = session.ksccmc; // 考试描述
    const examVenue = session.ksddmc; // 考试场地
    const examinationName = examCenter.kcmc; // 考场名称
    const examCarType = session.kscx; // 考试车型

    // 处理单个或多个考生信息
    const examineesData = Array.isArray(appointmentResults)
      ? appointmentResults
      : [appointmentResults];

    const validExaminees = examineesData.filter((item) => item.zt === "1");

    console.log(
      `场次 ${session.ksccmc} - 考试日期 ${new Date(
        session.ksrq
      ).toLocaleDateString()} - 车型 ${session.kscx} - API返回考生数量: ${
        validExaminees.length
      }/${session.yyrs}`
    );

    for (const examinee of validExaminees) {
      saveExamineeToDatabase(
        examinee,
        {
          examDate,
          examDesc,
          examCarType,
          examVenue,
          detailedAddress: examCenter.netSysPlacesite.lxdz || "",
          examSubject: kskm,
          examinationName,
        },
        db
      );
    }
  } catch (error) {
    console.error(`获取场次 ${session.xh} 的考生信息失败:`, error);
  }
}

/**
 * 保存考生信息到数据库
 */
function saveExamineeToDatabase(
  examinee: ExamineeData,
  examInfo: {
    examDate: string;
    examDesc: string;
    examCarType: string;
    examVenue: string;
    detailedAddress: string;
    examSubject: string;
    examinationName: string;
  },
  db: ExamineeDatabase
): void {
  db.insertExaminee({
    name: examinee.xm || "", // 姓名
    id_number: examinee.sfzmhm || "", // 身份证明号码
    allowed_car_type: examinee.kscx || "", // 准考车型
    appointment_result: examinee.zt || "", // 预约结果
    sort_time: examinee.pxsjStr ? new Date(examinee.pxsjStr).toISOString() : "", // 排序时间
    exam_date: examInfo.examDate, // 考试日期
    exam_desc: examInfo.examDesc, // 考试描述
    exam_car_type: examInfo.examCarType, // 考试车型
    exam_venue: examInfo.examVenue, // 考试场地
    detailed_address: examInfo.detailedAddress, // 详细地址
    exam_subject: examInfo.examSubject, // 考试科目
    created_at: new Date().toISOString(), // 创建时间
  });
}

/**
 * 爬取和处理所有科目的考试数据
 */
async function collectAllExamData(
  cookie: string,
  provinceCode: string,
  fzjg: string,
  db: ExamineeDatabase
): Promise<void> {
  // 处理1-4科目
  for (let kskm = 1; kskm <= 4; kskm++) {
    console.log(`正在统计科目${kskm}的预约数据...`);

    // 获取考场列表
    const examCenterList = await getExamCenterOptionsList({
      cookie,
      province: provinceCode,
      fzjg,
      kskm: kskm.toString(),
    });

    console.log(`科目${kskm}共有${examCenterList.length}个考场`);

    // 遍历每个考场
    for (const examCenter of examCenterList) {
      await processExamCenterData(
        examCenter,
        kskm.toString(),
        cookie,
        provinceCode,
        fzjg,
        db
      );
    }
  }
}

async function mainTask() {
  try {
    console.log(`[${new Date().toLocaleString()}] 开始执行数据采集任务...`);

    // 在函数内部初始化数据库
    const latestConfig = loadConfig();
    const db = new ExamineeDatabase(
      latestConfig.databasePath,
      latestConfig.statisticsDays
    );

    const fzjg = latestConfig.fzjg;
    const provinceCode = getProvinceCode(latestConfig.province);
    // const licenseName = getLicenseName(fzjg);
    const cookie = await getCookie({ province: provinceCode });

    // 动态计算日期范围
    const dateRange = calculateDateRange(db);
    CONFIG.dateRange = dateRange;

    console.log(`查询日期范围: ${dateRange.startDate} 至 ${dateRange.endDate}`);

    // TEST:
    // 收集和处理考试数据
    await collectAllExamData(cookie, provinceCode, fzjg, db);
    console.log(`[${new Date().toLocaleString()}] 数据采集任务完成`);

    // 分析数据库并展示统计结果
    await db.printStatistics(latestConfig.statisticsExaminationName);
    // await db.saveMarkdownTable("./database/statistics.md", licenseName);
    // await db.saveMarkdown2Image("./database/statistics.md");
    const date = new Date().getTime();
    await db.saveHtml(
      `./database/statistics-${date}.html`,
      latestConfig.statisticsExaminationName
    );

    // 关闭数据库连接
    db.close();

    await new Promise((resolve, reject) => {
      const { exec } = require("child_process");
      exec(
        `wkhtmltoimage --width 730 ./database/statistics-${date}.html ./database/statistics-${date}.png`,
        (error: Error | null, stdout: string, stderr: string) => {
          if (error) {
            console.error(`执行wkhtmltoimage出错: ${error}`);
            reject(error);
            return;
          }
          console.log(`生成统计图片成功: ${stdout}`);

          // 上传图片
          uploadFile({
            file: fs.readFileSync(`./database/statistics-${date}.png`),
            filename: `statistics-${date}.png`,
          }).then((uploadRes) => {
            console.log("[uploadRes]", uploadRes);

            // 推送消息
            const imageUrl = uploadRes.data.links.url;
            CONFIG.pushKey.forEach((pushKey) => {
              sendMessage({
                pushKey,
                imageUrl,
              });
            });
            console.log(`[${new Date().toLocaleString()}] 推送消息成功`);
          });
          resolve(stdout);
        }
      );
    });
  } catch (error) {
    console.error(
      `[${new Date().toLocaleString()}] 程序执行过程中发生错误:`,
      error
    );
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  // 立即执行一次
  mainTask().catch((error) => {
    console.error(
      `[${new Date().toLocaleString()}] 程序执行过程中发生错误:`,
      error
    );
  });

  // 使用 node-cron 设置定时任务
  console.log(`设置定时任务: ${CONFIG.cronSchedule}`);
  cron.schedule(CONFIG.cronSchedule, () => {
    console.log(`[${new Date().toLocaleString()}] 定时任务触发`);
    mainTask().catch((error) => {
      console.error(
        `[${new Date().toLocaleString()}] 程序执行过程中发生错误:`,
        error
      );
    });
  });
}

// 执行主函数
main().catch((error) => {
  console.error("程序执行过程中发生错误:", error);
});
