import path from "path";
import fs from "fs";

// 配置
interface ConfigType {
  province: string;
  fzjg: string;
  cronSchedule: string;
  databasePath: string;
  daysAhead: number;
  statisticsDays: number;
  pushKey: string[];
  dateRange: {
    startDate: string;
    endDate: string;
  };
  statisticsExaminationName: string;
}

// 默认配置
const DEFAULT_CONFIG: ConfigType = {
  province: "广东",
  fzjg: "粤K", // 城市车牌号
  cronSchedule: "0 8 * * *", // 每天早上 8 点执行
  databasePath: path.join(process.cwd(), "data", "examinees.db"),
  daysAhead: 15, // 默认向前查询15天的考试
  statisticsDays: 7, // 默认只统计最近7天的数据
  pushKey: [],
  dateRange: {
    startDate: new Date().toISOString().split("T")[0], // 默认开始日期为今天
    endDate: "", // 将在程序中动态设置
  },
  statisticsExaminationName: "",
};

// 从配置文件加载配置
function loadConfig(): ConfigType {
  const config = { ...DEFAULT_CONFIG };

  try {
    const configPath = path.join(process.cwd(), "app-config.json");
    if (fs.existsSync(configPath)) {
      const userConfig = JSON.parse(fs.readFileSync(configPath, "utf-8"));
      // 加载用户配置，覆盖默认值
      if (userConfig.province) config.province = userConfig.province;
      if (userConfig.fzjg) config.fzjg = userConfig.fzjg;
      if (userConfig.cron_schedule)
        config.cronSchedule = userConfig.cron_schedule;
      if (userConfig.days_ahead) config.daysAhead = userConfig.days_ahead;
      if (userConfig.statistics_days)
        config.statisticsDays = userConfig.statistics_days;
      if (userConfig.push_key) config.pushKey = userConfig.push_key;
      if (userConfig.database_path)
        config.databasePath = userConfig.database_path;
      if (userConfig.statistics_examination_name)
        config.statisticsExaminationName =
          userConfig.statistics_examination_name;

      // 日期范围会在程序中动态设置，这里只设置初始值
      config.dateRange = {
        startDate: config.dateRange.startDate,
        endDate: "",
      };

      console.log("成功从配置文件加载配置");
    } else {
      console.log("配置文件不存在，使用默认配置");
    }
  } catch (error) {
    console.error("加载配置文件失败:", error);
    console.log("使用默认配置");
  }

  return config;
}

export default loadConfig;
