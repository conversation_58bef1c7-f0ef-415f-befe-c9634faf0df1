import { getCaptch<PERSON>, get<PERSON>aptcha<PERSON><PERSON>, verifyCaptcha } from "../api";
import { detectGaps } from "../detect-gaps";

const htmlSeed = newSeed();

function getRandomNumberByRange(start: number, end: number) {
  return Math.floor(Math.random() * (end - start) + start);
}
function newSeed() {
  var num = getRandomNumberByRange(3, 6);
  var seed = "";
  for (var i = 0; i < num; i++) {
    seed += getRandomNumberByRange(1, 9);
  }
  return parseInt(seed);
}

const decryptY = (yCode: string) => {
  var y_index = 0; // 初始化为默认值
  var seed = htmlSeed;
  var arr = yCode.split(",");
  var bds = arr[arr.length - 1];

  var callThePageFunction = function (seed: number) {
    return (seed = seed > 0 ? seed - htmlSeed : seed + htmlSeed);
  };
  eval(bds);
  var y = arr[y_index];
  return y;
};

async function getToken({
  cookie,
  province,
}: {
  cookie: string;
  province: string;
}) {
  let attempts = 0;
  const MAX_ATTEMPTS = 15;

  while (attempts < MAX_ATTEMPTS) {
    attempts++;

    try {
      await getCaptchaPre({ cookie, province });
      const { data } = await getCaptcha({
        seed: htmlSeed.toString(),
        cookie,
        province,
      });
      const { background, y: yCode } = JSON.parse(data);

      const y = decryptY(yCode);
      const yValue = parseFloat(y);

      // 识别验证码缺口，根据环境传入测试模式参数
      const gapCoordinates = await detectGaps(background);

      if (!gapCoordinates || gapCoordinates.length !== 2) {
        throw new Error("未检测到足够的缺口");
      }

      // 使用reduce查找最接近y值的坐标对象
      const closestGap = gapCoordinates.reduce((closest, current) => {
        const currentDiff = Math.abs(yValue - current.topLeft.y);
        const closestDiff = Math.abs(yValue - closest.topLeft.y);
        return currentDiff < closestDiff ? current : closest;
      }, gapCoordinates[0]);

      // 计算缺口的偏移量
      const offsetX = closestGap.topLeft.x - 5;
      const { data: token, code } = await verifyCaptcha({
        x: offsetX.toString(),
        y,
        cookie,
        province,
      });

      if (code !== "C100000") {
        throw new Error("验证码验证失败");
      }

      return token;
    } catch (error: any) {
      if (attempts >= MAX_ATTEMPTS) {
        throw new Error(
          `获取token失败，已尝试${MAX_ATTEMPTS}次: ${error.message}`
        );
      }
      // 继续下一次循环尝试
    }
  }
}

export default getToken;
