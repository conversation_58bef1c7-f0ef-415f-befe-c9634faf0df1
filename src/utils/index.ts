import province from "../constans/province.json";
import license from "../constans/licenseName.json";

export const getProvinceCode = (provinceName: string) => {
  return province[provinceName as keyof typeof province];
};

export const getLicenseName = (licenseCode: string) => {
  return license[licenseCode as keyof typeof license];
};

/**
 * 获取本地日期的格式字符串（YYYY-MM-DD）
 * @param date 日期对象，默认为当前日期
 * @returns 本地日期字符串，格式为YYYY-MM-DD
 */
export function getLocalDateString(date = new Date()): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

/**
 * 生成统计报告文件名（混合命名策略）
 * @param prefix 文件名前缀，默认为"statistics"
 * @param extension 文件扩展名，默认为"html"
 * @param date 日期对象，默认为当前时间
 * @returns 格式为 {prefix}-{YYYY-MM-DD}-{timestamp}.{extension} 的文件名
 */
export function generateReportFilename(
  prefix: string = "statistics",
  extension: string = "html",
  date: Date = new Date()
): string {
  // 获取日期字符串 YYYY-MM-DD
  const dateStr = getLocalDateString(date);

  // 获取毫秒级时间戳
  const timestamp = date.getTime();

  // 组合文件名：prefix-YYYY-MM-DD-timestamp.extension
  return `${prefix}-${dateStr}-${timestamp}.${extension}`;
}

/**
 * 生成统计报告文件路径
 * @param prefix 文件名前缀，默认为"statistics"
 * @param extension 文件扩展名，默认为"html"
 * @param baseDir 基础目录，默认为"./database"
 * @param date 日期对象，默认为当前时间
 * @returns 完整的文件路径
 */
export function generateReportFilePath(
  prefix: string = "statistics",
  extension: string = "html",
  baseDir: string = "./database",
  date: Date = new Date()
): string {
  const filename = generateReportFilename(prefix, extension, date);
  return `${baseDir}/${filename}`;
}

/**
 * 从混合命名格式的文件名中提取时间戳
 * @param filename 文件名，格式为 prefix-YYYY-MM-DD-timestamp.extension
 * @returns 时间戳数字，如果解析失败返回null
 */
export function extractTimestampFromFilename(filename: string): number | null {
  // 匹配模式：prefix-YYYY-MM-DD-timestamp.extension
  const match = filename.match(/-(\d{4}-\d{2}-\d{2})-(\d+)\./);
  if (match && match[2]) {
    const timestamp = parseInt(match[2], 10);
    return isNaN(timestamp) ? null : timestamp;
  }
  return null;
}

/**
 * 从混合命名格式的文件名中提取日期
 * @param filename 文件名，格式为 prefix-YYYY-MM-DD-timestamp.extension
 * @returns 日期字符串（YYYY-MM-DD），如果解析失败返回null
 */
export function extractDateFromFilename(filename: string): string | null {
  // 匹配模式：prefix-YYYY-MM-DD-timestamp.extension
  const match = filename.match(/-(\d{4}-\d{2}-\d{2})-\d+\./);
  return match ? match[1] : null;
}
