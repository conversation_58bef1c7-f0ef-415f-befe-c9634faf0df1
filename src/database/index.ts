import initSqlJs from "sql.js";
import type { Database } from "sql.js";
import path from "path";
import fs from "fs";
import { getLocalDateString } from "../utils";
import { fileURLToPath } from "url";

interface StatisticsData {
  subject1: { exam_date: string; count: number }[];
  subject4: { exam_date: string; count: number }[];
  subject2: {
    C1: number;
    C2: number;
    C5: number;
    C1BySession?: { [session: string]: number };
    C2BySession?: { [session: string]: number };
    C5BySession?: { [session: string]: number };
  };
  subject3: {
    C1: number;
    C2: number;
    C5: number;
    C1BySession?: { [session: string]: number };
    C2BySession?: { [session: string]: number };
    C5BySession?: { [session: string]: number };
  };
  subject2ByDate: {
    exam_date: string;
    C1: number;
    C2: number;
    C5: number;
  }[];
  subject3ByDate: {
    exam_date: string;
    C1: number;
    C2: number;
    C5: number;
  }[];
  subject2ByDateAndSession: {
    [exam_date: string]: {
      C1Total: number;
      C2Total: number;
      C5Total: number;
      C1BySessions: { [session: string]: number };
      C2BySessions: { [session: string]: number };
      C5BySessions: { [session: string]: number };
    };
  };
  subject3ByDateAndSession: {
    [exam_date: string]: {
      C1Total: number;
      C2Total: number;
      C5Total: number;
      C1BySessions: { [session: string]: number };
      C2BySessions: { [session: string]: number };
      C5BySessions: { [session: string]: number };
    };
  };
}

/**
 * 数据库操作类
 */
class ExamineeDatabase {
  private db: Database;
  private statisticsDays: number = 3; // 默认统计最近3天的数据
  private dbPath: string;

  constructor(dbPath: string, statisticsDays?: number) {
    // 确保数据库目录存在
    const dbDir = path.dirname(dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    this.dbPath = dbPath;

    // 如果传入了统计天数，则使用传入的值
    if (statisticsDays) {
      this.statisticsDays = statisticsDays;
    }

    // 初始化数据库将在异步方法中完成
    this.db = null as unknown as Database;
    this.initializeDatabase();
  }

  public updateStatisticsDays(days: number): void {
    this.statisticsDays = days;
  }

  /**
   * 初始化数据库表
   */
  private async initializeDatabase(): Promise<void> {
    try {
      // 初始化 SQL.js
      const SQL = await initSqlJs();

      // 检查文件是否存在，如果存在则读取
      let dbData: Uint8Array | undefined;
      if (fs.existsSync(this.dbPath)) {
        dbData = new Uint8Array(fs.readFileSync(this.dbPath));
      }

      // 如果有现有数据，则加载它；否则创建新数据库
      this.db = dbData ? new SQL.Database(dbData) : new SQL.Database();

      // 创建表（如果不存在）
      this.db.run(`
        CREATE TABLE IF NOT EXISTS examinees (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT,                  -- 姓名
          id_number TEXT,             -- 身份证号
          allowed_car_type TEXT,      -- 准考车型
          appointment_result TEXT,    -- 预约结果
          sort_time TEXT,             -- 排序时间
          exam_date TEXT,             -- 考试日期
          exam_desc TEXT,             -- 考试描述
          exam_car_type TEXT,         -- 考试车型
          exam_venue TEXT,            -- 考试场地
          detailed_address TEXT,      -- 详细地址
          exam_subject TEXT,          -- 考试科目 1-4 分别代表科目 1-4
          created_at TEXT             -- 创建时间
        )
      `);

      // 保存数据库状态
      this.saveDatabase();
    } catch (error) {
      console.error("初始化数据库失败:", error);
      throw error;
    }
  }

  /**
   * 保存数据库到文件
   */
  private saveDatabase(): void {
    const data = this.db.export();
    fs.writeFileSync(this.dbPath, new Uint8Array(data));
  }

  /**
   * 插入考生信息
   */
  insertExaminee(data: {
    name: string;
    id_number: string;
    allowed_car_type: string;
    appointment_result: string;
    sort_time: string;
    exam_date: string;
    exam_desc: string;
    exam_car_type: string;
    exam_venue: string;
    detailed_address: string;
    exam_subject: string;
    created_at: string;
  }): void {
    this.db.run(
      `
      INSERT INTO examinees (
        name, id_number, allowed_car_type, appointment_result, sort_time,
        exam_date, exam_desc, exam_car_type, exam_venue,
        detailed_address, exam_subject, created_at
      ) VALUES (
        $name, $id_number, $allowed_car_type, $appointment_result, $sort_time,
        $exam_date, $exam_desc, $exam_car_type, $exam_venue,
        $detailed_address, $exam_subject, $created_at
      )
    `,
      {
        $name: data.name,
        $id_number: data.id_number,
        $allowed_car_type: data.allowed_car_type,
        $appointment_result: data.appointment_result,
        $sort_time: data.sort_time,
        $exam_date: data.exam_date,
        $exam_desc: data.exam_desc,
        $exam_car_type: data.exam_car_type,
        $exam_venue: data.exam_venue,
        $detailed_address: data.detailed_address,
        $exam_subject: data.exam_subject,
        $created_at: data.created_at,
      }
    );

    // 保存变更到文件
    this.saveDatabase();
  }

  /**
   * 获取统计数据
   */
  async getStatistics(examinationName: string): Promise<StatisticsData> {
    // 计算日期范围，使用数据库中的最新考试日期作为结束日期
    const latestExamDate = this.getLatestExamDate();
    const endDate = latestExamDate ? new Date(latestExamDate) : new Date();
    const startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - this.statisticsDays);

    const startDateStr = getLocalDateString(startDate);
    const endDateStr = getLocalDateString(endDate);

    console.log(
      `统计考场: ${examinationName}，日期范围: ${startDateStr} 至 ${endDateStr}`
    );

    // 用于存储数据库查询统计结果
    const statistics: StatisticsData = {
      subject1: [],
      subject4: [],
      subject2: {
        C1: 0,
        C2: 0,
        C5: 0,
        C1BySession: {},
        C2BySession: {},
        C5BySession: {},
      },
      subject3: {
        C1: 0,
        C2: 0,
        C5: 0,
        C1BySession: {},
        C2BySession: {},
        C5BySession: {},
      },
      subject2ByDate: [],
      subject3ByDate: [],
      subject2ByDateAndSession: {},
      subject3ByDateAndSession: {},
    };

    // 科目1每日预约人数
    const subject1Result = this.db.exec(`
      SELECT exam_date, COUNT(*) as count 
      FROM examinees 
      WHERE exam_venue = '${examinationName}'
      AND exam_subject = '1' 
      AND exam_date >= '${startDateStr}'
      AND exam_date <= '${endDateStr}'
      GROUP BY exam_date 
      ORDER BY exam_date
    `);

    if (subject1Result.length > 0 && subject1Result[0].values.length > 0) {
      statistics.subject1 = subject1Result[0].values.map((row) => ({
        exam_date: row[0] as string,
        count: row[1] as number,
      }));
    }

    // 科目4每日预约人数
    const subject4Result = this.db.exec(`
      SELECT exam_date, COUNT(*) as count 
      FROM examinees 
      WHERE exam_venue = '${examinationName}'
      AND exam_subject = '4' 
      AND exam_date >= '${startDateStr}'
      AND exam_date <= '${endDateStr}'
      GROUP BY exam_date 
      ORDER BY exam_date
    `);

    if (subject4Result.length > 0 && subject4Result[0].values.length > 0) {
      statistics.subject4 = subject4Result[0].values.map((row) => ({
        exam_date: row[0] as string,
        count: row[1] as number,
      }));
    }

    // 获取科目2车型统计
    statistics.subject2 = this.getSubjectCarTypeStats(
      "2",
      startDateStr,
      endDateStr,
      examinationName
    );

    // 获取科目3车型统计
    statistics.subject3 = this.getSubjectCarTypeStats(
      "3",
      startDateStr,
      endDateStr,
      examinationName
    );

    // 获取科目2按日期和车型统计
    statistics.subject2ByDate = this.getSubjectCarTypeByDateStats(
      "2",
      startDateStr,
      endDateStr,
      examinationName
    );

    // 获取科目3按日期和车型统计
    statistics.subject3ByDate = this.getSubjectCarTypeByDateStats(
      "3",
      startDateStr,
      endDateStr,
      examinationName
    );

    // 获取科目2按场次和车型统计
    this.getSubjectCarTypeBySessionStats(
      "2",
      startDateStr,
      endDateStr,
      examinationName,
      statistics.subject2
    );

    // 获取科目3按场次和车型统计
    this.getSubjectCarTypeBySessionStats(
      "3",
      startDateStr,
      endDateStr,
      examinationName,
      statistics.subject3
    );

    // 获取科目2按日期和场次统计
    this.getSubjectByDateAndSessionStats(
      "2",
      startDateStr,
      endDateStr,
      examinationName,
      statistics.subject2ByDateAndSession
    );

    // 获取科目3按日期和场次统计
    this.getSubjectByDateAndSessionStats(
      "3",
      startDateStr,
      endDateStr,
      examinationName,
      statistics.subject3ByDateAndSession
    );

    return statistics;
  }

  /**
   * 获取指定科目的车型统计数据
   */
  private getSubjectCarTypeStats(
    subject: string,
    startDate: string,
    endDate: string,
    examinationName: string
  ): {
    C1: number;
    C2: number;
    C5: number;
  } {
    const carTypeCount = { C1: 0, C2: 0, C5: 0 };

    const result = this.db.exec(`
      SELECT 
        CASE 
          WHEN exam_car_type = 'C1' THEN 'C1'
          WHEN exam_car_type = 'C2' THEN 'C2'
          WHEN exam_car_type = 'C5' THEN 'C5'
          WHEN exam_car_type LIKE '%C1%' THEN 'Mixed-C1'
          WHEN exam_car_type LIKE '%C2%' THEN 'Mixed-C2'
          WHEN exam_car_type LIKE '%C5%' THEN 'Mixed-C5'
          ELSE 'Other'
        END as car_type,
        COUNT(*) as count
      FROM examinees
      WHERE exam_subject = '${subject}'
      AND exam_venue = '${examinationName}'
      AND exam_date >= '${startDate}'
      AND exam_date <= '${endDate}'
      GROUP BY car_type
      ORDER BY car_type
    `);

    if (result.length > 0 && result[0].values.length > 0) {
      // 统计各车型人数，处理混合类型
      result[0].values.forEach((row) => {
        const carType = row[0] as string;
        const count = row[1] as number;

        if (carType === "C1") {
          carTypeCount.C1 += count;
        } else if (carType === "C2") {
          carTypeCount.C2 += count;
        } else if (carType === "C5") {
          carTypeCount.C5 += count;
        } else if (carType === "Mixed-C1") {
          // 混合类型中的C1，按比例分配
          carTypeCount.C1 += Math.floor(count / 2);
        } else if (carType === "Mixed-C2") {
          // 混合类型中的C2，按比例分配
          carTypeCount.C2 += Math.floor(count / 2);
        } else if (carType === "Mixed-C5") {
          // 混合类型中的C5，按比例分配
          carTypeCount.C5 += Math.floor(count / 2);
        }
      });
    }

    return carTypeCount;
  }

  /**
   * 获取指定科目按日期和车型的统计数据
   */
  private getSubjectCarTypeByDateStats(
    subject: string,
    startDate: string,
    endDate: string,
    examinationName: string
  ): { exam_date: string; C1: number; C2: number; C5: number }[] {
    const subjectDateMap = new Map<
      string,
      { C1: number; C2: number; C5: number }
    >();

    const result = this.db.exec(`
      SELECT 
        exam_date,
        CASE 
          WHEN exam_car_type = 'C1' THEN 'C1'
          WHEN exam_car_type = 'C2' THEN 'C2'
          WHEN exam_car_type = 'C5' THEN 'C5'
          WHEN exam_car_type LIKE '%C1%' THEN 'Mixed-C1'
          WHEN exam_car_type LIKE '%C2%' THEN 'Mixed-C2'
          WHEN exam_car_type LIKE '%C5%' THEN 'Mixed-C5'
          ELSE 'Other'
        END as car_type,
        COUNT(*) as count
      FROM examinees
      WHERE exam_subject = '${subject}'
      AND exam_venue = '${examinationName}'
      AND exam_date >= '${startDate}'
      AND exam_date <= '${endDate}'
      GROUP BY exam_date, car_type
      ORDER BY exam_date, car_type
    `);

    if (result.length > 0 && result[0].values.length > 0) {
      // 初始化每个日期的数据
      result[0].values.forEach((row) => {
        const examDate = row[0] as string;
        if (!subjectDateMap.has(examDate)) {
          subjectDateMap.set(examDate, { C1: 0, C2: 0, C5: 0 });
        }
      });

      // 填充车型数据
      result[0].values.forEach((row) => {
        const examDate = row[0] as string;
        const carType = row[1] as string;
        const count = row[2] as number;
        const dateData = subjectDateMap.get(examDate)!;

        if (carType === "C1") {
          dateData.C1 += count;
        } else if (carType === "C2") {
          dateData.C2 += count;
        } else if (carType === "C5") {
          dateData.C5 += count;
        } else if (carType === "Mixed-C1") {
          dateData.C1 += Math.floor(count / 2);
        } else if (carType === "Mixed-C2") {
          dateData.C2 += Math.floor(count / 2);
        } else if (carType === "Mixed-C5") {
          dateData.C5 += Math.floor(count / 2);
        }
      });
    }

    // 转换为数组
    return Array.from(subjectDateMap.entries())
      .map(([exam_date, data]) => ({
        exam_date,
        ...data,
      }))
      .sort((a, b) => a.exam_date.localeCompare(b.exam_date));
  }

  /**
   * 获取指定科目按场次和车型的统计数据
   */
  private getSubjectCarTypeBySessionStats(
    subject: string,
    startDate: string,
    endDate: string,
    examinationName: string,
    subjectStats: {
      C1: number;
      C2: number;
      C5: number;
      C1BySession?: { [session: string]: number };
      C2BySession?: { [session: string]: number };
      C5BySession?: { [session: string]: number };
    }
  ): void {
    // 确保会话统计字段已初始化
    if (!subjectStats.C1BySession) subjectStats.C1BySession = {};
    if (!subjectStats.C2BySession) subjectStats.C2BySession = {};
    if (!subjectStats.C5BySession) subjectStats.C5BySession = {};

    const result = this.db.exec(`
      SELECT 
        exam_date,
        exam_desc,
        exam_venue,
        CASE 
          WHEN exam_car_type = 'C1' THEN 'C1'
          WHEN exam_car_type = 'C2' THEN 'C2'
          WHEN exam_car_type = 'C5' THEN 'C5'
          WHEN exam_car_type LIKE '%C1%' THEN 'Mixed-C1'
          WHEN exam_car_type LIKE '%C2%' THEN 'Mixed-C2'
          WHEN exam_car_type LIKE '%C5%' THEN 'Mixed-C5'
          ELSE 'Other'
        END as car_type,
        COUNT(*) as count
      FROM examinees
      WHERE exam_subject = '${subject}'
      AND exam_venue = '${examinationName}'
      AND exam_date >= '${startDate}'
      AND exam_date <= '${endDate}'
      GROUP BY exam_date, exam_venue, exam_desc, car_type
      ORDER BY exam_date, exam_venue, exam_desc, car_type
    `);

    if (result.length > 0 && result[0].values.length > 0) {
      // 从时间段中提取开始时间的函数
      const extractStartTime = (
        timeSlot: string
      ): { time: string; minutes: number } => {
        // 尝试匹配"xx:xx至xx:xx"格式
        const match = timeSlot.match(/(\d{1,2}):?(\d{2})(?:至|\s*-\s*|~).*?/i);
        if (match) {
          // 标准化小时和分钟
          const hour = match[1].padStart(2, "0");
          const minute = match[2];
          const timeStr = `${hour}:${minute}`;
          // 转换为分钟数
          const totalMinutes = parseInt(hour) * 60 + parseInt(minute);
          return { time: timeStr, minutes: totalMinutes };
        }
        return { time: "00:00", minutes: 0 }; // 默认值
      };

      // 转换为中文数字的函数
      const toChineseNumber = (num: number): string => {
        const chineseNumbers = [
          "一",
          "二",
          "三",
          "四",
          "五",
          "六",
          "七",
          "八",
          "九",
          "十",
        ];
        if (num >= 1 && num <= 10) {
          return chineseNumbers[num - 1];
        }
        return String(num);
      };

      // 建议的修改方案
      interface TimeSlot {
        examDesc: string;
        carType: string;
        count: number;
      }

      // 按日期、考场、车型分组
      const dateVenueCarGroups = new Map<
        string, // 日期
        Map<
          string, // 考场
          Map<
            string, // 车型
            TimeSlot[]
          >
        >
      >();

      // 处理数据
      result[0].values.forEach((row) => {
        const examDate = row[0] as string;
        const examDesc = row[1] as string;
        const examVenue = row[2] as string;
        const carType = row[3] as string;
        const count = row[4] as number;

        // 初始化数据结构
        if (!dateVenueCarGroups.has(examDate)) {
          dateVenueCarGroups.set(examDate, new Map());
        }
        const venueMap = dateVenueCarGroups.get(examDate)!;
        
        if (!venueMap.has(examVenue)) {
          venueMap.set(examVenue, new Map());
        }
        const carMap = venueMap.get(examVenue)!;
        
        if (!carMap.has(carType)) {
          carMap.set(carType, []);
        }

        // 添加时间段数据
        if (examDesc && examDesc.includes("至")) {
          carMap.get(carType)!.push({
            examDesc,
            carType,
            count
          });
        }
      });

      // 为每个日期、考场、车型的时间段分配场次
      dateVenueCarGroups.forEach((venueMap, date) => {
        venueMap.forEach((carMap, venue) => {
          carMap.forEach((timeSlots, carType) => {
            // 提取开始时间并排序
            const timeInfoMap = new Map<string, { original: string; startTime: string; minutes: number }>();
            
            timeSlots.forEach((slot) => {
              const startTimeInfo = extractStartTime(slot.examDesc);
              timeInfoMap.set(slot.examDesc, {
                original: slot.examDesc,
                startTime: startTimeInfo.time,
                minutes: startTimeInfo.minutes
              });
            });

            // 按开始时间排序
            const sortedTimeSlots = timeSlots.sort((a, b) => {
              return timeInfoMap.get(a.examDesc)!.minutes - timeInfoMap.get(b.examDesc)!.minutes;
            });

            // 为当前车型的时间段分配场次
            const sessionMap = new Map<string, string>();
            sortedTimeSlots.forEach((slot, index) => {
              const sessionIndex = index + 1;
              const sessionName = `第${toChineseNumber(sessionIndex)}场`;
              sessionMap.set(slot.examDesc, sessionName);
            });

            // 更新统计数据
            sortedTimeSlots.forEach((slot) => {
              const session = sessionMap.get(slot.examDesc)!;
              if (carType === "C1") {
                subjectStats.C1BySession![session] =
                  (subjectStats.C1BySession![session] || 0) + slot.count;
              } else if (carType === "C2") {
                subjectStats.C2BySession![session] =
                  (subjectStats.C2BySession![session] || 0) + slot.count;
              } else if (carType === "C5") {
                subjectStats.C5BySession![session] =
                  (subjectStats.C5BySession![session] || 0) + slot.count;
              } else if (carType === "Mixed-C1") {
                subjectStats.C1BySession![session] =
                  (subjectStats.C1BySession![session] || 0) +
                  Math.floor(slot.count / 2);
              } else if (carType === "Mixed-C2") {
                subjectStats.C2BySession![session] =
                  (subjectStats.C2BySession![session] || 0) +
                  Math.floor(slot.count / 2);
              } else if (carType === "Mixed-C5") {
                subjectStats.C5BySession![session] =
                  (subjectStats.C5BySession![session] || 0) +
                  Math.floor(slot.count / 2);
              }
            });
          });
        });
      });
    }
  }

  /**
   * 获取指定科目按日期和场次的统计数据
   */
  private getSubjectByDateAndSessionStats(
    subject: string,
    startDate: string,
    endDate: string,
    examinationName: string,
    subjectDateSessionStats: {
      [exam_date: string]: {
        C1Total: number;
        C2Total: number;
        C5Total: number;
        C1BySessions: { [session: string]: number };
        C2BySessions: { [session: string]: number };
        C5BySessions: { [session: string]: number };
      };
    }
  ): void {
    const result = this.db.exec(`
      SELECT 
        exam_date,
        exam_desc,
        exam_venue,
        CASE 
          WHEN exam_car_type = 'C1' THEN 'C1'
          WHEN exam_car_type = 'C2' THEN 'C2'
          WHEN exam_car_type = 'C5' THEN 'C5'
          WHEN exam_car_type LIKE '%C1%' THEN 'Mixed-C1'
          WHEN exam_car_type LIKE '%C2%' THEN 'Mixed-C2'
          WHEN exam_car_type LIKE '%C5%' THEN 'Mixed-C5'
          ELSE 'Other'
        END as car_type,
        COUNT(*) as count
      FROM examinees
      WHERE exam_subject = '${subject}'
      AND exam_venue = '${examinationName}'
      AND exam_date >= '${startDate}'
      AND exam_date <= '${endDate}'
      GROUP BY exam_date, exam_venue, exam_desc, car_type
      ORDER BY exam_date, exam_venue, exam_desc, car_type
    `);

    if (result.length > 0 && result[0].values.length > 0) {
      // 从时间段中提取开始时间的函数
      const extractStartTime = (
        timeSlot: string
      ): { time: string; minutes: number } => {
        // 尝试匹配"xx:xx至xx:xx"格式
        const match = timeSlot.match(/(\d{1,2}):?(\d{2})(?:至|\s*-\s*|~).*?/i);
        if (match) {
          // 标准化小时和分钟
          const hour = match[1].padStart(2, "0");
          const minute = match[2];
          const timeStr = `${hour}:${minute}`;
          // 转换为分钟数
          const totalMinutes = parseInt(hour) * 60 + parseInt(minute);
          return { time: timeStr, minutes: totalMinutes };
        }
        return { time: "00:00", minutes: 0 }; // 默认值
      };

      // 转换为中文数字的函数
      const toChineseNumber = (num: number): string => {
        const chineseNumbers = [
          "一",
          "二",
          "三",
          "四",
          "五",
          "六",
          "七",
          "八",
          "九",
          "十",
        ];
        if (num >= 1 && num <= 10) {
          return chineseNumbers[num - 1];
        }
        return String(num);
      };

      // 按日期、考场、车型分组
      const dateVenueCarGroups = new Map<
        string, // 日期
        Map<
          string, // 考场
          Map<
            string, // 车型
            Array<{
              examDesc: string;
              carType: string;
              count: number;
            }>
          >
        >
      >();

      // 处理数据
      result[0].values.forEach((row) => {
        const examDate = row[0] as string;
        const examDesc = row[1] as string;
        const examVenue = row[2] as string;
        const carType = row[3] as string;
        const count = row[4] as number;

        // 初始化数据结构
        if (!dateVenueCarGroups.has(examDate)) {
          dateVenueCarGroups.set(examDate, new Map());
        }
        const venueMap = dateVenueCarGroups.get(examDate)!;
        
        if (!venueMap.has(examVenue)) {
          venueMap.set(examVenue, new Map());
        }
        const carMap = venueMap.get(examVenue)!;
        
        if (!carMap.has(carType)) {
          carMap.set(carType, []);
        }

        // 添加时间段数据
        if (examDesc && examDesc.includes("至")) {
          carMap.get(carType)!.push({
            examDesc,
            carType,
            count
          });
        }
      });

      // 为每个日期初始化统计数据
      dateVenueCarGroups.forEach((venueMap, date) => {
        if (!subjectDateSessionStats[date]) {
          subjectDateSessionStats[date] = {
            C1Total: 0,
            C2Total: 0,
            C5Total: 0,
            C1BySessions: {},
            C2BySessions: {},
            C5BySessions: {}
          };
        }
      });

      // 处理每个日期、考场、车型的数据
      dateVenueCarGroups.forEach((venueMap, date) => {
        const dateStats = subjectDateSessionStats[date];

        venueMap.forEach((carMap, venue) => {
          carMap.forEach((timeSlots, carType) => {
            // 提取开始时间并排序
            const timeInfoMap = new Map<string, { original: string; startTime: string; minutes: number }>();
            
            timeSlots.forEach((slot) => {
              const startTimeInfo = extractStartTime(slot.examDesc);
              timeInfoMap.set(slot.examDesc, {
                original: slot.examDesc,
                startTime: startTimeInfo.time,
                minutes: startTimeInfo.minutes
              });
            });

            // 按开始时间排序
            const sortedTimeSlots = timeSlots.sort((a, b) => {
              return timeInfoMap.get(a.examDesc)!.minutes - timeInfoMap.get(b.examDesc)!.minutes;
            });

            // 为当前车型的时间段分配场次
            const sessionMap = new Map<string, string>();
            sortedTimeSlots.forEach((slot, index) => {
              const sessionIndex = index + 1;
              const sessionName = `第${toChineseNumber(sessionIndex)}场`;
              sessionMap.set(slot.examDesc, sessionName);
            });

            // 更新统计数据
            sortedTimeSlots.forEach((slot) => {
              const session = sessionMap.get(slot.examDesc)!;
              const count = slot.count;

              if (carType === "C1") {
                dateStats.C1Total += count;
                dateStats.C1BySessions[session] = (dateStats.C1BySessions[session] || 0) + count;
              } else if (carType === "C2") {
                dateStats.C2Total += count;
                dateStats.C2BySessions[session] = (dateStats.C2BySessions[session] || 0) + count;
              } else if (carType === "C5") {
                dateStats.C5Total += count;
                dateStats.C5BySessions[session] = (dateStats.C5BySessions[session] || 0) + count;
              } else if (carType === "Mixed-C1") {
                const adjustedCount = Math.floor(count / 2);
                dateStats.C1Total += adjustedCount;
                dateStats.C1BySessions[session] = (dateStats.C1BySessions[session] || 0) + adjustedCount;
              } else if (carType === "Mixed-C2") {
                const adjustedCount = Math.floor(count / 2);
                dateStats.C2Total += adjustedCount;
                dateStats.C2BySessions[session] = (dateStats.C2BySessions[session] || 0) + adjustedCount;
              } else if (carType === "Mixed-C5") {
                const adjustedCount = Math.floor(count / 2);
                dateStats.C5Total += adjustedCount;
                dateStats.C5BySessions[session] = (dateStats.C5BySessions[session] || 0) + adjustedCount;
              }
            });
          });
        });
      });
    }
  }

  /**
   * 获取数据库中最远的考试日期
   * @returns 最远的考试日期，如果没有记录则返回 null
   */
  getLatestExamDate(): string | null {
    console.log("[getLatestExamDate]", this.db);
    const result = this.db.exec(`
      SELECT exam_date
      FROM examinees
      ORDER BY exam_date DESC
      LIMIT 1
    `);

    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0] as string;
    }
    return null;
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      // 保存最后的更改
      this.saveDatabase();
      // 关闭数据库连接
      this.db.close();
    }
  }

  /**
   * 分析数据库并打印统计结果
   */
  async printStatistics(examinationName: string): Promise<void> {
    console.log("\n========== 数据库查询统计结果 ==========");

    const statistics = await this.getStatistics(examinationName);

    // 科目1每日预约人数
    console.log("\n科目1每日预约人数:");
    statistics.subject1.forEach((row) => {
      console.log(`  ${row.exam_date}: ${row.count}人`);
    });

    // 科目2按日期和车型预约人数，以及按场次统计
    console.log("\n科目2按日期、车型和场次预约人数:");
    Object.entries(statistics.subject2ByDateAndSession)
      .sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
      .forEach(([date, dateStats]) => {
        console.log(`${date}:`);

        // C1统计
        if (dateStats.C1Total > 0) {
          console.log(`  C1: ${dateStats.C1Total}人`);
          Object.entries(dateStats.C1BySessions)
            .sort(([sessionA], [sessionB]) => sessionA.localeCompare(sessionB))
            .forEach(([session, count]) => {
              console.log(`  C1 ${session}: ${count}人`);
            });
        }

        // C2统计
        if (dateStats.C2Total > 0) {
          console.log(`  C2: ${dateStats.C2Total}人`);
          Object.entries(dateStats.C2BySessions)
            .sort(([sessionA], [sessionB]) => sessionA.localeCompare(sessionB))
            .forEach(([session, count]) => {
              console.log(`  C2 ${session}: ${count}人`);
            });
        }

        // C5统计
        if (dateStats.C5Total > 0) {
          console.log(`  C5: ${dateStats.C5Total}人`);
          Object.entries(dateStats.C5BySessions)
            .sort(([sessionA], [sessionB]) => sessionA.localeCompare(sessionB))
            .forEach(([session, count]) => {
              console.log(`  C5 ${session}: ${count}人`);
            });
        }
      });

    // 科目3按日期和车型预约人数，以及按场次统计
    console.log("\n科目3按日期、车型和场次预约人数:");
    Object.entries(statistics.subject3ByDateAndSession)
      .sort(([dateA], [dateB]) => dateA.localeCompare(dateB))
      .forEach(([date, dateStats]) => {
        console.log(`${date}:`);

        // C1统计
        if (dateStats.C1Total > 0) {
          console.log(`  C1: ${dateStats.C1Total}人`);
          Object.entries(dateStats.C1BySessions)
            .sort(([sessionA], [sessionB]) => sessionA.localeCompare(sessionB))
            .forEach(([session, count]) => {
              console.log(`  C1 ${session}: ${count}人`);
            });
        }

        // C2统计
        if (dateStats.C2Total > 0) {
          console.log(`  C2: ${dateStats.C2Total}人`);
          Object.entries(dateStats.C2BySessions)
            .sort(([sessionA], [sessionB]) => sessionA.localeCompare(sessionB))
            .forEach(([session, count]) => {
              console.log(`  C2 ${session}: ${count}人`);
            });
        }

        // C5统计
        if (dateStats.C5Total > 0) {
          console.log(`  C5: ${dateStats.C5Total}人`);
          Object.entries(dateStats.C5BySessions)
            .sort(([sessionA], [sessionB]) => sessionA.localeCompare(sessionB))
            .forEach(([session, count]) => {
              console.log(`  C5 ${session}: ${count}人`);
            });
        }
      });

    // 科目4每日预约人数
    console.log("\n科目4每日预约人数:");
    statistics.subject4.forEach((row) => {
      console.log(`  ${row.exam_date}: ${row.count}人`);
    });
  }

  /**
   * 生成Markdown表格格式的统计数据
   */
  async generateHTMLTable(examinationName: string): Promise<string> {
    const statistics = await this.getStatistics(examinationName);
    let markdownTable = "";

    // 获取所有有数据的日期
    const allDates = new Set<string>();
    [
      ...Object.keys(statistics.subject2ByDateAndSession),
      ...Object.keys(statistics.subject3ByDateAndSession),
    ].forEach((date) => {
      allDates.add(date);
    });

    // 提取科目1和科目4的数据
    const subject1Map = new Map<string, number>();
    statistics.subject1.forEach((item) => {
      subject1Map.set(item.exam_date, item.count);
    });

    const subject4Map = new Map<string, number>();
    statistics.subject4.forEach((item) => {
      subject4Map.set(item.exam_date, item.count);
    });

    // 获取所有可能的场次名称并按顺序排列
    const allSessionNames = new Set<string>();

    // 从科目2和科目3的数据中收集所有场次名称
    Object.values(statistics.subject2ByDateAndSession).forEach((dateStats) => {
      Object.keys(dateStats.C1BySessions).forEach((session) =>
        allSessionNames.add(session)
      );
      Object.keys(dateStats.C2BySessions).forEach((session) =>
        allSessionNames.add(session)
      );
    });

    Object.values(statistics.subject3ByDateAndSession).forEach((dateStats) => {
      Object.keys(dateStats.C1BySessions).forEach((session) =>
        allSessionNames.add(session)
      );
      Object.keys(dateStats.C2BySessions).forEach((session) =>
        allSessionNames.add(session)
      );
    });

    // 将场次名称转换为数组并排序
    const sessionOrder = [
      "第一场",
      "第二场",
      "第三场",
      "第四场",
      "第五场",
      "第六场",
      "第七场",
      "第八场",
      "第九场",
      "第十场",
    ];
    const sortedSessionNames = Array.from(allSessionNames).sort((a, b) => {
      const indexA = sessionOrder.indexOf(a);
      const indexB = sessionOrder.indexOf(b);
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      } else if (indexA !== -1) {
        return -1;
      } else if (indexB !== -1) {
        return 1;
      }
      return a.localeCompare(b);
    });

    // 最多显示的场次数（为了表格美观）
    const maxSessionsToShow = Math.min(sortedSessionNames.length, 99999); // 每个科目和车型最多显示3个场次
    const sessionsToShow = sortedSessionNames.slice(0, maxSessionsToShow);

    console.log(`找到的场次: ${Array.from(allSessionNames).join(", ")}`);
    console.log(`将显示的场次: ${sessionsToShow.join(", ")}`);

    // 使用HTML表格格式
    markdownTable += `<h2 style="width:730px;text-align:center;">${examinationName}预约考试人数</h2>\n\n`;
    markdownTable +=
      "<table style='text-align:center; border-collapse:collapse;color:black;background-color:white;'>\n";

    // 表头第一行
    markdownTable += "<tr>\n";
    markdownTable +=
      "  <th rowspan='3' style='border:1px solid #ccc;white-space: nowrap;'>考试日期</th>\n";
    markdownTable +=
      "  <th rowspan='3' style='border:1px solid #ccc;white-space: nowrap;'>星期</th>\n";
    markdownTable +=
      "  <th colspan='3' style='border:1px solid #ccc;white-space: nowrap; background-color:#FFCC00;'>理论</th>\n";
    markdownTable += `  <th colspan='${
      maxSessionsToShow * 3 + 1
    }' style='border:1px solid #ccc;white-space: nowrap; background-color:#5B9BD5;'>科目二</th>\n`;
    markdownTable += `  <th colspan='${
      maxSessionsToShow * 3 + 1
    }' style='border:1px solid #ccc;white-space: nowrap; background-color:#70AD47;'>科目三</th>\n`;
    markdownTable += "</tr>\n";

    // 表头第二行
    markdownTable += "<tr>\n";
    markdownTable +=
      "  <th rowspan='2' style='border:1px solid #ccc;white-space: nowrap;'>科一</th>\n";
    markdownTable +=
      "  <th rowspan='2' style='border:1px solid #ccc;white-space: nowrap;'>科四</th>\n";
    markdownTable +=
      "  <th rowspan='2' style='border:1px solid #ccc;white-space: nowrap; background-color:#FFFF00;'>合计</th>\n";
    markdownTable += `  <th colspan='${maxSessionsToShow}' style='border:1px solid #ccc;white-space: nowrap;'>C1</th>\n`;
    markdownTable += `  <th colspan='${maxSessionsToShow}' style='border:1px solid #ccc;white-space: nowrap;'>C2</th>\n`;
    markdownTable += `  <th colspan='${maxSessionsToShow}' style='border:1px solid #ccc;white-space: nowrap;'>C5</th>\n`;
    markdownTable +=
      "  <th rowspan='2' style='border:1px solid #ccc;white-space: nowrap; background-color:#FFFF00;'>合计</th>\n";
    markdownTable += `  <th colspan='${maxSessionsToShow}' style='border:1px solid #ccc;white-space: nowrap;'>C1</th>\n`;
    markdownTable += `  <th colspan='${maxSessionsToShow}' style='border:1px solid #ccc;white-space: nowrap;'>C2</th>\n`;
    markdownTable += `  <th colspan='${maxSessionsToShow}' style='border:1px solid #ccc;white-space: nowrap;'>C5</th>\n`;
    markdownTable +=
      "  <th rowspan='2' style='border:1px solid #ccc;white-space: nowrap; background-color:#FFFF00;'>合计</th>\n";
    markdownTable += "</tr>\n";

    // 表头第三行 - 动态生成场次列
    markdownTable += "<tr>\n";

    // 科目二 C1 场次
    sessionsToShow.forEach((session) => {
      markdownTable += `  <th style='border:1px solid #ccc;white-space: nowrap;'>${session}</th>\n`;
    });

    // 科目二 C2 场次
    sessionsToShow.forEach((session) => {
      markdownTable += `  <th style='border:1px solid #ccc;white-space: nowrap;'>${session}</th>\n`;
    });

    // 科目二 C5 场次
    sessionsToShow.forEach((session) => {
      markdownTable += `  <th style='border:1px solid #ccc;white-space: nowrap;'>${session}</th>\n`;
    });

    // 科目三 C1 场次
    sessionsToShow.forEach((session) => {
      markdownTable += `  <th style='border:1px solid #ccc;white-space: nowrap;'>${session}</th>\n`;
    });

    // 科目三 C2 场次
    sessionsToShow.forEach((session) => {
      markdownTable += `  <th style='border:1px solid #ccc;white-space: nowrap;'>${session}</th>\n`;
    });

    // 科目三 C5 场次
    sessionsToShow.forEach((session) => {
      markdownTable += `  <th style='border:1px solid #ccc;white-space: nowrap;'>${session}</th>\n`;
    });

    markdownTable += "</tr>\n";

    // 计算科目总和
    let totalSubject1 = 0;
    let totalSubject4 = 0;
    let totalSubject2 = 0;
    let totalSubject3 = 0;

    // 按日期排序
    const sortedDates = Array.from(allDates).sort();

    // 日期数据
    for (const date of sortedDates) {
      // 跳过空日期
      if (!date || date.trim() === "") continue;

      // 计算星期几
      const dayOfWeek = this.getDayOfWeek(date);

      // 科目1人数
      const subject1Count = subject1Map.get(date) || 0;
      totalSubject1 += subject1Count;

      // 科目4人数
      const subject4Count = subject4Map.get(date) || 0;
      totalSubject4 += subject4Count;

      // 理论合计
      const theoryTotal = subject1Count + subject4Count;

      // 科目二数据
      const subject2Data = statistics.subject2ByDateAndSession[date] || {
        C1Total: 0,
        C2Total: 0,
        C5Total: 0,
        C1BySessions: {},
        C2BySessions: {},
        C5BySessions: {},
      };

      // 科目三数据
      const subject3Data = statistics.subject3ByDateAndSession[date] || {
        C1Total: 0,
        C2Total: 0,
        C5Total: 0,
        C1BySessions: {},
        C2BySessions: {},
        C5BySessions: {},
      };

      // 科目二合计
      const subject2Total = subject2Data.C1Total + subject2Data.C2Total + subject2Data.C5Total;
      totalSubject2 += subject2Total;

      // 科目三合计
      const subject3Total = subject3Data.C1Total + subject3Data.C2Total + subject3Data.C5Total;
      totalSubject3 += subject3Total;

      // 添加行数据
      markdownTable += "<tr>\n";
      markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;background-color:#B4C6E7;'>${date}</td>\n`;
      markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;background-color:#B4C6E7;'>${dayOfWeek}</td>\n`;
      markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;'>${subject1Count}</td>\n`;
      markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;'>${subject4Count}</td>\n`;
      markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap; background-color:#FFFF00;'>${theoryTotal}</td>\n`;

      // 科目二 C1 各场次数据
      sessionsToShow.forEach((session) => {
        const count = subject2Data.C1BySessions[session] || 0;
        markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;'>${count}</td>\n`;
      });

      // 科目二 C2 各场次数据
      sessionsToShow.forEach((session) => {
        const count = subject2Data.C2BySessions[session] || 0;
        markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;'>${count}</td>\n`;
      });

      // 科目二 C5 各场次数据
      sessionsToShow.forEach((session) => {
        const count = subject2Data.C5BySessions[session] || 0;
        markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;'>${count}</td>\n`;
      });

      markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap; background-color:#FFFF00;'>${subject2Total}</td>\n`;

      // 科目三 C1 各场次数据
      sessionsToShow.forEach((session) => {
        const count = subject3Data.C1BySessions[session] || 0;
        markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;'>${count}</td>\n`;
      });

      // 科目三 C2 各场次数据
      sessionsToShow.forEach((session) => {
        const count = subject3Data.C2BySessions[session] || 0;
        markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;'>${count}</td>\n`;
      });

      // 科目三 C5 各场次数据
      sessionsToShow.forEach((session) => {
        const count = subject3Data.C5BySessions[session] || 0;
        markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap;'>${count}</td>\n`;
      });

      markdownTable += `  <td style='border:1px solid #ccc;white-space: nowrap; background-color:#FFFF00;'>${subject3Total}</td>\n`;
      markdownTable += "</tr>\n";
    }

    // 添加合计行
    const totalTheory = totalSubject1 + totalSubject4;
    // const grandTotal = totalTheory + totalSubject2 + totalSubject3;
    markdownTable += "<tr>\n";
    markdownTable += `  <td colspan='2' style='border:1px solid #ccc;white-space: nowrap;'>科目周合计</td>\n`;
    markdownTable += `  <td colspan='3' style='border:1px solid #ccc;white-space: nowrap;'>${totalTheory}</td>\n`;
    markdownTable += `  <td colspan='${
      maxSessionsToShow * 3 + 1
    }' style='border:1px solid #ccc;white-space: nowrap;'>${totalSubject2}</td>\n`;
    markdownTable += `  <td colspan='${
      maxSessionsToShow * 3 + 1
    }' style='border:1px solid #ccc;white-space: nowrap;'>${totalSubject3}</td>\n`;
    markdownTable += "</tr>\n";

    markdownTable += "</table>";

    return markdownTable;
  }

  /**
   * 根据日期获取星期几
   */
  private getDayOfWeek(dateString: string): string {
    const date = new Date(dateString);
    const dayOfWeekNumber = date.getDay(); // 0是星期日，1-6是星期一至星期六

    const dayOfWeekMap: { [key: number]: string } = {
      0: "周日",
      1: "周一",
      2: "周二",
      3: "周三",
      4: "周四",
      5: "周五",
      6: "周六",
    };

    return dayOfWeekMap[dayOfWeekNumber] || "";
  }

  /**
   * 将生成的Markdown表格保存到文件
   * @param filePath 文件保存路径
   */
  async saveMarkdownTable(
    filePath: string,
    licenseName: string
  ): Promise<void> {
    try {
      const markdownContent = await this.generateHTMLTable(licenseName);
      const directory = path.dirname(filePath);

      // 确保目录存在
      if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
      }

      // 写入文件
      fs.writeFileSync(filePath, markdownContent, "utf8");
      console.log(`成功保存统计表格到: ${filePath}`);
    } catch (error) {
      console.error("保存Markdown表格失败:", error);
      throw error;
    }
  }

  async saveHtml(filePath: string, licenseName: string): Promise<void> {
    const htmlContent = await this.generateHTMLTable(licenseName);
    const htmlTemplate = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<style>
  body {
    font-family: "Microsoft YaHei", "SimSun", "Arial", sans-serif;
  }
</style>
</head>
<body>
  ${htmlContent}
</body>
</html>
`;
    fs.writeFileSync(filePath, htmlTemplate, "utf8");
  }

  async saveMarkdown2Image(filePath: string): Promise<void> {
    // const convertRes = await mdimg({
    //   // inputFilename: filePath,
    //   inputText: "# Hello world",
    //   htmlTemplate: "<div style='width: 600px;'>{{content}}</div>",
    //   outputFilename: "./database/statisticsxx.png",
    //   // width: 600,
    //   extensions: true,
    // });
    // console.log(
    //   `Convert to image successfully!\nImage has been saved as \`${convertRes.path}\``
    // );
  }
}

export default ExamineeDatabase;
