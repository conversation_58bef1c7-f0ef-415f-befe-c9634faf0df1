import Jimp from "jimp";
import path from "path";
import fs from "fs";
import { URL } from "url";

// 定义类型
interface Region {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  pixelCount: number;
}

interface Point {
  x: number;
  y: number;
}

interface GapCoordinate {
  id: number;
  topLeft: Point;
  bottomRight: Point;
  width: number;
  height: number;
  pixelCount: number;
}

interface OriginalImage {
  name: string;
  image: Jimp;
}

// 定义路径
const originalImagesDir = new URL("./assets/background", import.meta.url)
  .pathname;
const outputPath = "./output-with-gaps.png";
const coordsOutputPath = "./gap-coordinates.json";

// 比较两个像素是否相同 (包含容差)
const pixelDiff = (pixel1: number, pixel2: number, threshold = 20): boolean => {
  const r1 = Jimp.intToRGBA(pixel1).r;
  const g1 = Jimp.intToRGBA(pixel1).g;
  const b1 = Jimp.intToRGBA(pixel1).b;

  const r2 = Jimp.intToRGBA(pixel2).r;
  const g2 = Jimp.intToRGBA(pixel2).g;
  const b2 = Jimp.intToRGBA(pixel2).b;

  const diff = Math.sqrt(
    Math.pow(r1 - r2, 2) + Math.pow(g1 - g2, 2) + Math.pow(b1 - b2, 2)
  );

  return diff > threshold;
};

// 比较左上角区域 (15x15像素)
const compareTopLeftCorner = (img1: Jimp, img2: Jimp): number => {
  let diffCount = 0;

  for (let x = 0; x < 15; x++) {
    for (let y = 0; y < 15; y++) {
      if (pixelDiff(img1.getPixelColor(x, y), img2.getPixelColor(x, y))) {
        diffCount++;
      }
    }
  }

  return diffCount;
};

// 查找缺口位置 - 使用更精确的算法
const findGaps = (original: Jimp, background: Jimp): Region[] => {
  const width = original.getWidth();
  const height = original.getHeight();

  // 创建差异图
  const diffMap = new Array(height).fill(0).map(() => new Array(width).fill(0));

  // 计算差异
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const originalPixel = original.getPixelColor(x, y);
      const backgroundPixel = background.getPixelColor(x, y);

      if (pixelDiff(originalPixel, backgroundPixel)) {
        diffMap[y][x] = 1;
      }
    }
  }

  // 使用连通区域算法找到缺口区域
  const gaps: Region[] = [];
  const visited = new Array(height)
    .fill(0)
    .map(() => new Array(width).fill(false));

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      if (diffMap[y][x] === 1 && !visited[y][x]) {
        // 找到一个新的差异点，开始BFS搜索连通区域
        const region: Region = {
          startX: x,
          startY: y,
          endX: x,
          endY: y,
          pixelCount: 0,
        };

        const queue: Point[] = [{ x, y }];
        visited[y][x] = true;

        while (queue.length > 0) {
          const current = queue.shift();
          // 确保current不为undefined
          if (!current) continue;

          region.pixelCount++;

          // 更新区域边界
          region.startX = Math.min(region.startX, current.x);
          region.startY = Math.min(region.startY, current.y);
          region.endX = Math.max(region.endX, current.x);
          region.endY = Math.max(region.endY, current.y);

          // 检查四个方向的相邻像素
          const directions = [
            { dx: -1, dy: 0 }, // 左
            { dx: 1, dy: 0 }, // 右
            { dx: 0, dy: -1 }, // 上
            { dx: 0, dy: 1 }, // 下
          ];

          for (const dir of directions) {
            const nx = current.x + dir.dx;
            const ny = current.y + dir.dy;

            if (
              nx >= 0 &&
              nx < width &&
              ny >= 0 &&
              ny < height &&
              diffMap[ny][nx] === 1 &&
              !visited[ny][nx]
            ) {
              queue.push({ x: nx, y: ny });
              visited[ny][nx] = true;
            }
          }
        }

        // 只添加足够大的区域（过滤噪点）
        if (
          region.pixelCount > 50 &&
          region.endX - region.startX > 5 &&
          region.endY - region.startY > 5
        ) {
          gaps.push(region);
        }
      }
    }
  }

  // 按区域大小排序，取最大的几个区域
  gaps.sort((a, b) => b.pixelCount - a.pixelCount);
  return gaps.slice(0, 2); // 返回最大的两个区域
};

async function detectGaps(
  backgroundBase64: string | null = null
): Promise<GapCoordinate[] | undefined> {
  try {
    let background: Jimp;

    if (backgroundBase64) {
      // 直接从内存中读取图像
      const imageBuffer = Buffer.from(backgroundBase64, "base64");
      background = await Jimp.read(imageBuffer);
    } else {
      // 如果没有提供base64，则从报错
      console.error("backgroundBase64 不存在");
      return;
    }

    // 读取所有原始图片
    const originalImages: OriginalImage[] = [];
    const files = fs.readdirSync(originalImagesDir);

    for (const file of files) {
      if (file.endsWith(".png") && !file.startsWith(".")) {
        const imagePath = path.join(originalImagesDir, file);
        const image = await Jimp.read(imagePath);
        originalImages.push({
          name: file,
          image,
        });
      }
    }

    // 找到最匹配的原始图片
    let bestMatch: OriginalImage | null = null;
    let lowestDiffCount = Infinity;

    for (const original of originalImages) {
      const diffCount = compareTopLeftCorner(original.image, background);
      if (diffCount < lowestDiffCount) {
        lowestDiffCount = diffCount;
        bestMatch = original;
      }
    }

    // 确保bestMatch不为null
    if (!bestMatch) {
      console.error("未找到匹配的原始图片");
      return;
    }

    // 查找缺口位置
    const gaps = findGaps(bestMatch.image, background);

    // 准备坐标数据
    const gapCoordinates: GapCoordinate[] = [];

    // 处理每个缺口
    for (let i = 0; i < gaps.length; i++) {
      const gap = gaps[i];

      // 保存坐标信息
      gapCoordinates.push({
        id: i + 1,
        topLeft: { x: gap.startX, y: gap.startY },
        bottomRight: { x: gap.endX, y: gap.endY },
        width: gap.endX - gap.startX,
        height: gap.endY - gap.startY,
        pixelCount: gap.pixelCount,
      });
    }

    // 仅在测试模式下执行以下操作
    const isTestMode = process.env.NODE_ENV === "development";
    if (isTestMode) {
      // 复制原始图片，用于标记缺口
      const outputImage = bestMatch.image.clone();

      // 在原图上绘制红框标记缺口
      for (let i = 0; i < gaps.length; i++) {
        const gap = gaps[i];

        // 绘制红框 - 线条加粗
        const lineWidth = 2;

        // 绘制水平线（上下边框）
        for (let x = gap.startX - lineWidth; x <= gap.endX + lineWidth; x++) {
          for (let t = 0; t < lineWidth; t++) {
            // 上边框
            if (gap.startY - t >= 0) {
              outputImage.setPixelColor(
                Jimp.rgbaToInt(255, 0, 0, 255),
                x,
                gap.startY - t
              );
            }
            // 下边框
            if (gap.endY + t < outputImage.getHeight()) {
              outputImage.setPixelColor(
                Jimp.rgbaToInt(255, 0, 0, 255),
                x,
                gap.endY + t
              );
            }
          }
        }

        // 绘制垂直线（左右边框）
        for (let y = gap.startY - lineWidth; y <= gap.endY + lineWidth; y++) {
          for (let t = 0; t < lineWidth; t++) {
            // 左边框
            if (gap.startX - t >= 0) {
              outputImage.setPixelColor(
                Jimp.rgbaToInt(255, 0, 0, 255),
                gap.startX - t,
                y
              );
            }
            // 右边框
            if (gap.endX + t < outputImage.getWidth()) {
              outputImage.setPixelColor(
                Jimp.rgbaToInt(255, 0, 0, 255),
                gap.endX + t,
                y
              );
            }
          }
        }
      }

      // 创建差异图，显示原图和验证码图片的差异
      const diffImage = bestMatch.image.clone();
      for (let y = 0; y < diffImage.getHeight(); y++) {
        for (let x = 0; x < diffImage.getWidth(); x++) {
          const originalPixel = bestMatch.image.getPixelColor(x, y);
          const backgroundPixel = background.getPixelColor(x, y);

          if (pixelDiff(originalPixel, backgroundPixel)) {
            diffImage.setPixelColor(Jimp.rgbaToInt(255, 0, 0, 255), x, y);
          }
        }
      }

      // 保存处理后的图片
      await outputImage.writeAsync(outputPath);
      await diffImage.writeAsync("./output-diff.png");

      // 保存坐标信息到JSON文件
      fs.writeFileSync(
        coordsOutputPath,
        JSON.stringify(
          {
            originalImage: bestMatch.name,
            gaps: gapCoordinates,
          },
          null,
          2
        )
      );

      // 在控制台以表格形式输出缺口坐标信息
      console.log("\n缺口坐标信息汇总:");
      console.log("--------------------------------------");
      console.log("编号  |  左上角X  |  左上角Y  |  宽度  |  高度");
      console.log("--------------------------------------");
      gapCoordinates.forEach((gap) => {
        console.log(
          `${gap.id.toString().padEnd(5)} | ${gap.topLeft.x
            .toString()
            .padEnd(9)} | ${gap.topLeft.y.toString().padEnd(9)} | ${gap.width
            .toString()
            .padEnd(6)} | ${gap.height}`
        );
      });
      console.log("--------------------------------------");
    }

    return gapCoordinates;
  } catch (error) {
    console.error("发生错误:", error);
  }
}

// detectGaps();
export { detectGaps };
