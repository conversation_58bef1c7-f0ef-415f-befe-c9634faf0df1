#!/bin/bash

# 设置变量
PROJECT_NAME="examinees-collection"
BUILD_DIR="build"
# SEA配置文件路径
SEA_CONFIG="sea-config.json"
# Windows版Node.js下载目录
WIN_NODE_DIR="${BUILD_DIR}/win-node"
WIN_NODE_EXE="${WIN_NODE_DIR}/node.exe"

# 处理命令行参数
PLATFORM=""
while [[ $# -gt 0 ]]; do
  case $1 in
    --mac)
      PLATFORM="mac"
      shift
      ;;
    --win)
      PLATFORM="win"
      shift
      ;;
    *)
      shift
      ;;
  esac
done

# 捕获错误并终止脚本
set -e

# 显示执行的命令
set -x

# 清空 build 目录
echo "setup: 清空build目录"
rm -rf ${BUILD_DIR}

# 编译TypeScript
echo "步骤1: 编译TypeScript"
npm run build

# 创建输出目录
echo "步骤2: 创建输出目录"
mkdir -p ${BUILD_DIR}

# 下载Windows版Node.js
download_windows_node() {
  echo "下载Windows版Node.js..."
  mkdir -p ${WIN_NODE_DIR}
  
  # 获取当前node版本
  NODE_VERSION=$(node -v)
  # 移除版本号前的v字符
  NODE_VERSION=${NODE_VERSION#v}
  
  # 下载Windows版Node.js
  WIN_NODE_URL="https://nodejs.org/dist/v${NODE_VERSION}/win-x64/node.exe"
  echo "从 ${WIN_NODE_URL} 下载Windows版Node.js..."
  
  # 使用curl下载
  if ! curl -L "${WIN_NODE_URL}" -o "${WIN_NODE_EXE}"; then
    echo "下载失败，尝试其他版本..."
    # 如果下载失败，尝试固定的LTS版本
    WIN_NODE_URL="https://nodejs.org/dist/v18.16.1/win-x64/node.exe"
    curl -L "${WIN_NODE_URL}" -o "${WIN_NODE_EXE}" || return 1
  fi
  
  # 检查下载是否成功
  if [ ! -f "${WIN_NODE_EXE}" ]; then
    echo "下载Windows版Node.js失败"
    return 1
  fi
  
  echo "下载完成：${WIN_NODE_EXE}"
  return 0
}

# 定义构建函数
build_for_platform() {
  local platform=$1
  local output_name="${BUILD_DIR}/${PROJECT_NAME}"
  local sea_blob_path="${output_name}"
  
  if [[ "$platform" == "mac" ]]; then
    output_name="${output_name}-mac"
    
    # 更新SEA配置文件输出路径
    node -e "const config = require('./${SEA_CONFIG}'); config.output = '${sea_blob_path}-mac-blob'; require('fs').writeFileSync('./${SEA_CONFIG}', JSON.stringify(config, null, 2));"
    
    # 使用Node.js生成blob文件
    echo "步骤3: 生成blob文件 (macOS)"
    node --experimental-sea-config ${SEA_CONFIG}
    
    # 复制node可执行文件
    echo "步骤4: 复制Node.js可执行文件 (macOS)"
    cp $(command -v node) ${output_name}.tmp
    
    # macOS特有步骤
    echo "步骤5: 移除签名 (macOS)"
    codesign --remove-signature ${output_name}.tmp
    
    echo "步骤6: 注入blob到可执行文件 (macOS)"
    npx postject ${output_name}.tmp NODE_SEA_BLOB ${sea_blob_path}-mac-blob \
      --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2 \
      --macho-segment-name NODE_SEA
    
    echo "步骤7: 重新签名 (macOS)"
    codesign --sign - ${output_name}.tmp
    
  elif [[ "$platform" == "win" ]]; then
    output_name="${output_name}-win.exe"
    
    # 更新SEA配置文件输出路径
    node -e "const config = require('./${SEA_CONFIG}'); config.output = '${sea_blob_path}-win-blob'; require('fs').writeFileSync('./${SEA_CONFIG}', JSON.stringify(config, null, 2));"
    
    # 使用Node.js生成blob文件
    echo "步骤3: 生成blob文件 (Windows)"
    node --experimental-sea-config ${SEA_CONFIG}
    
    # 复制node可执行文件
    echo "步骤4: 复制Node.js可执行文件 (Windows)"
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
      # 如果在Windows上构建
      cp $(command -v node) ${output_name}.tmp
    else
      # 如果在非Windows系统上构建Windows版本
      # 尝试下载Windows版Node.js
      if [ ! -f "${WIN_NODE_EXE}" ]; then
        download_windows_node || {
          echo "错误: 无法下载Windows版本的Node.js可执行文件"
          return 1
        }
      fi
      
      # 使用下载的Windows Node.js
      cp "${WIN_NODE_EXE}" ${output_name}.tmp
    fi
    
    # Windows特有步骤
    echo "步骤5: 注入blob到可执行文件 (Windows)"
    npx postject ${output_name}.tmp NODE_SEA_BLOB ${sea_blob_path}-win-blob \
      --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2
  else
    echo "不支持的平台: $platform"
    return 1
  fi
  
  # 移动到最终输出文件
  echo "步骤6: 移动到最终输出文件 ($platform)"
  mv ${output_name}.tmp ${output_name}
  
  # 添加执行权限
  echo "步骤7: 添加执行权限 ($platform)"
  chmod +x ${output_name}
  
  echo "构建完成：${output_name}"
  file ${output_name}
}

# 构建各平台版本
if [[ "$PLATFORM" == "mac" ]]; then
  echo "仅构建 macOS 版本"
  build_for_platform "mac"
elif [[ "$PLATFORM" == "win" ]]; then
  echo "仅构建 Windows 版本"
  build_for_platform "win"
else
  echo "构建所有平台版本"
  build_for_platform "mac"
  build_for_platform "win"
fi

# 创建压缩包
echo "步骤8: 创建程序压缩包"
ZIP_NAME="${PROJECT_NAME}-$(date +%Y%m%d).zip"
# 添加要打包的文件和目录
(cd ${BUILD_DIR} && zip -r ${ZIP_NAME} * -x "*-blob" -x "win-node/*")

# 显示完成信息
echo "压缩包已创建：${BUILD_DIR}/${ZIP_NAME}"

echo "可以通过以下命令运行:"
if [[ "$PLATFORM" == "mac" || -z "$PLATFORM" ]]; then
  echo "macOS: ./${BUILD_DIR}/${PROJECT_NAME}-mac" 
fi
if [[ "$PLATFORM" == "win" || -z "$PLATFORM" ]]; then
  echo "Windows: ./${BUILD_DIR}/${PROJECT_NAME}-win.exe"
fi

# 清理临时文件
rm -f ${BUILD_DIR}/*-blob 