{"name": "examinees-collection", "version": "1.0.0", "description": "检测缺口验证码", "main": "index.js", "type": "module", "scripts": {"build": "tsdown --sourcemap", "build:debug": "tsdown --sourcemap --format=esm", "build:windows": "./build.sh --win", "build:mac": "./build.sh --mac", "start": "npm run build && node dist/index.cjs", "start:debug": "tsdown --sourcemap --format=esm && node dist/index.js", "detect": "node detect-gaps.js"}, "dependencies": {"jimp": "^0.22.10", "node-cron": "^3.0.3", "sql.js": "^1.13.0", "undici": "^7.8.0"}, "devDependencies": {"@types/node-cron": "^3.0.11", "@types/sql.js": "^1.4.9", "tsdown": "^0.11.1", "typescript": "^5.8.3"}}