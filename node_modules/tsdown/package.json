{"name": "tsdown", "version": "0.11.13", "description": "The Elegant Bundler for Libraries", "type": "module", "license": "MIT", "homepage": "https://github.com/rolldown/tsdown#readme", "bugs": {"url": "https://github.com/rolldown/tsdown/issues"}, "repository": {"type": "git", "url": "git+https://github.com/rolldown/tsdown.git"}, "author": "三咲智子 <PERSON> <<EMAIL>>", "funding": "https://github.com/sponsors/sxzz", "files": ["dist", "esm-shims.js"], "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./config": "./dist/config.js", "./plugins": "./dist/plugins.js", "./package.json": "./package.json"}, "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "bin": {"tsdown": "./dist/run.js"}, "publishConfig": {"access": "public"}, "peerDependencies": {"publint": "^0.3.0", "typescript": "^5.0.0", "unplugin-lightningcss": "^0.4.0", "unplugin-unused": "^0.5.0"}, "peerDependenciesMeta": {"publint": {"optional": true}, "typescript": {"optional": true}, "unplugin-lightningcss": {"optional": true}, "unplugin-unused": {"optional": true}}, "dependencies": {"ansis": "^4.0.0", "cac": "^6.7.14", "chokidar": "^4.0.3", "debug": "^4.4.1", "diff": "^8.0.1", "empathic": "^1.1.0", "hookable": "^5.5.3", "rolldown": "1.0.0-beta.9", "rolldown-plugin-dts": "^0.13.3", "semver": "^7.7.2", "tinyexec": "^1.0.1", "tinyglobby": "^0.2.13", "unconfig": "^7.3.2"}, "devDependencies": {"@oxc-node/core": "^0.0.27", "@sxzz/eslint-config": "^7.0.1", "@sxzz/prettier-config": "^2.2.1", "@sxzz/test-utils": "^0.5.6", "@types/debug": "^4.1.12", "@types/node": "^22.15.21", "@types/semver": "^7.7.0", "@unocss/eslint-plugin": "^66.1.2", "bumpp": "^10.1.1", "eslint": "^9.27.0", "lightningcss": "^1.30.1", "pkg-types": "^2.1.0", "prettier": "^3.5.3", "publint": "^0.3.12", "typedoc": "^0.28.4", "typedoc-plugin-markdown": "^4.6.3", "typescript": "~5.8.3", "unocss": "^66.1.2", "unplugin-lightningcss": "^0.4.0", "unplugin-unused": "^0.5.0", "vite": "^6.3.5", "vitepress": "^1.6.3", "vitepress-plugin-group-icons": "^1.5.5", "vitepress-plugin-llms": "^1.3.2", "vitest": "^3.1.4", "vue": "^3.5.14"}, "engines": {"node": ">=18.0.0"}, "prettier": "@sxzz/prettier-config", "scripts": {"lint": "eslint --cache --max-warnings 0 .", "lint:fix": "pnpm run lint --fix", "build": "node --import @oxc-node/core/register ./src/run.ts", "dev": "node --import @oxc-node/core/register ./src/run.ts", "test": "vitest", "typecheck": "tsc --noEmit", "format": "prettier --cache --write .", "release": "bumpp && pnpm publish", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "docs:generate": "node --import @oxc-node/core/register ./docs/.vitepress/scripts/docs-generate.ts"}}