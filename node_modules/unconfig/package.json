{"name": "unconfig", "type": "module", "version": "7.3.3", "description": "A universal solution for loading configurations.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu-collective/unconfig#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu-collective/unconfig.git"}, "bugs": {"url": "https://github.com/antfu-collective/unconfig/issues"}, "keywords": ["config"], "sideEffects": false, "exports": {".": "./dist/index.mjs", "./presets": "./dist/presets.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["*.d.ts", "dist"], "dependencies": {"@quansync/fs": "^0.1.5", "defu": "^6.1.4", "jiti": "^2.5.1", "quansync": "^0.2.11"}, "devDependencies": {"@antfu/eslint-config": "^5.2.1", "@antfu/ni": "^25.0.0", "@antfu/utils": "^9.2.0", "@types/node": "^24.3.0", "bumpp": "^10.2.3", "eslint": "^9.33.0", "lodash-es": "^4.17.21", "tsx": "^4.20.4", "typescript": "^5.9.2", "unbuild": "^3.6.1", "unplugin-quansync": "^0.4.4", "vitest": "^3.2.4"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint", "release": "bumpp", "start": "tsx src/index.ts", "test": "vitest", "typecheck": "tsc"}}