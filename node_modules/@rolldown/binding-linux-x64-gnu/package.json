{"name": "@rolldown/binding-linux-x64-gnu", "version": "1.0.0-beta.9", "cpu": ["x64"], "main": "rolldown-binding.linux-x64-gnu.node", "files": ["rolldown-binding.linux-x64-gnu.node"], "description": "Fast JavaScript/TypeScript bundler in Rust with Rollup-compatible API.", "keywords": ["webpack", "parcel", "esbuild", "rollup", "bundler", "rolldown"], "homepage": "https://rolldown.rs/", "license": "MIT", "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "https://github.com/rolldown/rolldown.git", "directory": "packages/rolldown"}, "os": ["linux"], "libc": ["glibc"]}