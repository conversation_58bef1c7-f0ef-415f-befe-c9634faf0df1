{"name": "rolldown-plugin-dts", "version": "0.13.14", "description": "A Rolldown plugin to bundle dts files", "type": "module", "license": "MIT", "homepage": "https://github.com/sxzz/rolldown-plugin-dts#readme", "bugs": {"url": "https://github.com/sxzz/rolldown-plugin-dts/issues"}, "repository": {"type": "git", "url": "git+https://github.com/sxzz/rolldown-plugin-dts.git"}, "author": "三咲智子 <PERSON> <<EMAIL>>", "funding": "https://github.com/sponsors/sxzz", "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./filename": "./dist/filename.js", "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "peerDependencies": {"@typescript/native-preview": ">=7.0.0-dev.20250601.1", "rolldown": "^1.0.0-beta.9", "typescript": "^5.0.0", "vue-tsc": "^2.2.0 || ^3.0.0"}, "peerDependenciesMeta": {"@typescript/native-preview": {"optional": true}, "typescript": {"optional": true}, "vue-tsc": {"optional": true}}, "dependencies": {"@babel/generator": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/types": "^7.28.1", "ast-kit": "^2.1.1", "birpc": "^2.5.0", "debug": "^4.4.1", "dts-resolver": "^2.1.1", "get-tsconfig": "^4.10.1"}, "devDependencies": {"@sxzz/eslint-config": "^7.0.6", "@sxzz/prettier-config": "^2.2.3", "@sxzz/test-utils": "^0.5.6", "@types/babel__generator": "^7.27.0", "@types/debug": "^4.1.12", "@types/node": "^24.0.14", "@typescript/native-preview": "7.0.0-dev.20250715.1", "@volar/typescript": "^2.4.19", "@vue/language-core": "^3.0.1", "bumpp": "^10.2.0", "diff": "^8.0.2", "eslint": "^9.31.0", "estree-walker": "^3.0.3", "prettier": "^3.6.2", "rolldown": "^1.0.0-beta.27", "rollup-plugin-dts": "^6.2.1", "tinyglobby": "^0.2.14", "tsdown": "^0.12.9", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4", "vue": "^3.5.17", "vue-tsc": "^3.0.1"}, "engines": {"node": ">=20.18.0"}, "prettier": "@sxzz/prettier-config", "scripts": {"lint": "eslint --cache .", "lint:fix": "pnpm run lint --fix", "build": "tsdown", "dev": "tsdown --watch", "test": "vitest", "typecheck": "tsc --noEmit", "format": "prettier --cache --write .", "release": "bumpp && pnpm publish"}}