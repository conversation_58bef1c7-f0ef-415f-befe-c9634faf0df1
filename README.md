# 使用方法

## 开发

```bash
# 调试
npm run start

# 编译
npm run build:windows
npm run build:macos
```

## 配置说明

配置文件：`app-config.json`，包含以下选项：

| 配置项                      | 说明                                                                    |
| --------------------------- | ----------------------------------------------------------------------- |
| province                    | 省份名称，例如"广东"                                                    |
| fzjg                        | 城市车牌号，例如"粤 K"（表示茂名）                                      |
| cron_schedule               | 定时任务执行时间，采用 cron 表达式，默认"0 8 \* \* \*"（每天早上 8 点） |
| database_path               | 数据库存储路径，默认为"database/sqlite.db"                              |
| days_ahead                  | 当前向后查询的天数，默认 15 天                                          |
| statistics_days             | 统计报告数据的天数，默认 7 天                                           |
| statistics_examination_name | 考场名，根据此考场统计数据                                              |
| push_key                    | 消息推送 key 数组，可配置多个（http://www.pushdeer.com/official.html）  |

## 主程序

### 前置配置

> 启动前需要安装 [wkhtmltopdf](https://wkhtmltopdf.org/downloads.html)，安装好以后就不用再安装了

根据你的系统下载安装对应版本的 [wkhtmltopdf](https://wkhtmltopdf.org/downloads.html)

如果是 Windows 系统，安装完成后还需要将 [wkhtmltopdf](https://wkhtmltopdf.org/downloads.html) 的主程序所在的 bin 目录添加到环境变量，macOS 则不需要

### Windows

主程序名：`examinees-collection-win.exe` 直接运行即可

### macOS

主程序名：`examinees-collection-mac`，terminal 中直接运行即可

## 注意事项

- 数据库在首次运行时会自动创建（根据 config.json 中 database_path 作为路径）
